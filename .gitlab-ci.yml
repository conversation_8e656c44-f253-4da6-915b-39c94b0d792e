stages:
  - standalone # workflow:   N/A
  - build # workflows:  branch commit, MR, prod release
  - test # workflows:  branch commit, MR, prod release
  - deploy dynamic # workflow:   branch commit
  - push image # workflow:   prod release
  - non-prod deploy # workflow:   production release
  - staging regression # workflow:   production release
  - uat deploy # workflow:   production release
  - uat regression # workflow:   production release
  - deploy # workflows:  branch commit, MR, prod release
  - regression # workflows:  branch commit, MR
  - cleanup # workflows:  branch commit, scheduled, production release
  - post deploy # workflow:   production release
  - publish # workflow:   N/A

variables:
  AWS_DEFAULT_REGION: ap-southeast-2
  FF_SCRIPT_SECTIONS: 'true'
  SUZUKA_TF_MAPPINGS_TABLE: suzuka-dynamic-environment-TF-mappings

default:
  image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/devops/pipeline-images:tf-1.2.2-147
  tags:
    - newsnow.io/stability:on-demand-v4
    - newsnow.io/application:gitlab-runner-v4
    - newsnow.io/application-group:ci-v4

build: # workflows: branch commit, MR, prod release
  image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/devops/pipeline-images:assume-role-259
  stage: build
  services:
    - ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:24.0.4-dind
  variables:
    DOCKER_HOST: tcp://localhost:2375
    DOCKER_TLS_CERTDIR: ''
    DOCKER_DRIVER: overlay2
  before_script:
    - until docker info > /dev/null 2>&1; do echo -e "\033[32mPreparing dind service...\033[0m" && sleep 1; done
    - ECR_REGISTRY_URL="${DEVELOPMENT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - ECR_REGISTRY_URL_STAGING="${STAGING_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - ECR_REGISTRY_URL_UAT="${UAT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - image_tag_commit_sha=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
    - image_tag_pipeline_id=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    - eval "$(assume-role.sh development terraform)"
    - docker version
    - docker images
    - echo ${CI_COMMIT_REF_SLUG}
  script:
    - echo $CI_API_V4_URL
    - DOCKER_BUILDKIT=0 docker build 
      --build-arg GITLAB_ACCESS_USERNAME=${GITLAB_ACCESS_USERNAME} 
      --build-arg GITLAB_ACCESS_PASSWORD=${GITLAB_ACCESS_PASSWORD}
      -t ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      -t ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_pipeline_id} 
      .
    - docker push ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
    - docker push ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
    - |
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
      docker tag ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_commit_sha} ${ECR_REGISTRY_URL_STAGING}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker tag ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_pipeline_id} ${ECR_REGISTRY_URL_STAGING}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      eval "$(assume-role.sh staging terraform)"
      docker push ${ECR_REGISTRY_URL_STAGING}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker push ${ECR_REGISTRY_URL_STAGING}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
      docker tag ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_commit_sha} ${ECR_REGISTRY_URL_UAT}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker tag ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag_pipeline_id} ${ECR_REGISTRY_URL_UAT}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      eval "$(assume-role.sh uat terraform)"
      docker push ${ECR_REGISTRY_URL_UAT}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker push ${ECR_REGISTRY_URL_UAT}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      docker images
  only:
    - branches
    - merge_requests
    - master
  except:
    - schedules
  retry:
    max: 2
    when:
      - always
  allow_failure: false

deploy dynamic env: # workflow: branch commit
  stage: deploy dynamic
  variables:
    image_tag: ${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    TF_VAR_site: ${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}
    TF_VAR_environment_name: ${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}
    TF_VAR_django_environment: ${CI_COMMIT_REF_SLUG}
    TF_VAR_django_management_host: -edit.${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}.dev.newsnow.io
    TF_VAR_django_suzuka_url: https://suzuka-${CI_COMMIT_REF_SLUG}.dev.newsnow.io/
    TF_VAR_app_image: racetracks/${CI_PROJECT_NAME}:${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    TF_VAR_environment: ${CI_COMMIT_REF_SLUG}
    ECR_REGISTRY_URL: ${DEVELOPMENT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks
    GIT_STRATEGY: none
  script:
    - set -euo pipefail
    - eval "$(assume-role.sh dev terraform)"
    - git init
    - git remote add origin ${CI_REPOSITORY_URL}
    - git fetch --depth=1 --filter=blob:none origin ${CI_COMMIT_SHA}
    - git checkout FETCH_HEAD -- restore_dynamodb_dynamic_env.py
    - ls -lh restore_dynamodb_dynamic_env.py
    
    - |
      query_template='{":v1": {"S": "%s"}}'
      printf "$query_template" ${image_tag%-*} > attributes.json
      tf_commit_sha=$(aws dynamodb query --table-name $SUZUKA_TF_MAPPINGS_TABLE --key-condition-expression "branch_name = :v1" --expression-attribute-values file://attributes.json | jq -r '.Items | .[] | .sha.S')
    - export TF_VAR_pgdatabase_dynamic=${CI_PROJECT_NAME}_${CI_COMMIT_REF_SLUG//[^A-z0-9_]/_}
    - match_host_header="${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}.dev.newsnow.io"
    - env
    - git config --global user.name "acm-bot-pr"
    - git config --global user.email "<EMAIL>"
    - printf "https://gitlab-ci-token:%<EMAIL>\n" $CI_JOB_TOKEN > ~/.git-credentials
    - git config --global credential.helper 'store --file ~/.git-credentials'
    - git clone -b master https://gitlab.com/fairfax-acm/deployers/tf_suzuka_ecs_service.git
    - cd tf_${CI_PROJECT_NAME}_ecs_service
    - |
      if [[ ! -z $tf_commit_sha ]]
      then
        git checkout $tf_commit_sha
      else
        tf_commit_sha=$(git rev-parse HEAD)
        template='{"branch_name":{"S":"%s"}, "sha":{"S":"%s"}}'
        printf "$template" ${image_tag%-*} $tf_commit_sha > item.json
        aws dynamodb put-item --table-name $SUZUKA_TF_MAPPINGS_TABLE --item file://item.json
      fi
    - ls
    - terraform init -backend-config=`pwd`/dynamic/backend-dynamic-main.conf -backend=true -lock=true --lock-timeout=120s
    - terraform workspace select ${image_tag%-*} || terraform workspace new ${image_tag%-*}
    - terraform workspace list
    # Check if monaco with the same branch name
    - length=$(aws route53 list-resource-record-sets --hosted-zone-id Z6AOLES4CPEEI --query "ResourceRecordSets[?Name == 'monaco-${CI_COMMIT_REF_SLUG}.app.newsnow.development.']" | jq length)
    - echo $length
    - |
      if [[ $length -eq 1 ]]
      then
       export TF_VAR_monaco_host="http://monaco-${CI_COMMIT_REF_SLUG}.app.newsnow.development"
      else
        export TF_VAR_monaco_host="http://monaco.app.newsnow.development"
      fi
    - echo ${TF_VAR_monaco_host}
    - terraform apply -lock=true --lock-timeout=120s -var-file=`pwd`/dynamic/dynamic-main.tfvars -auto-approve
    - terraform output -raw ecs_cluster_name > ecs_cluster_name
    - terraform output -raw task_definition_family > task_definition
    - terraform output -raw task_definition_dynamic_family > task_definition_dynamic
    - cat ecs_cluster_name task_definition task_definition_dynamic
    - sleep 15
    - |
      cluster="$(< ecs_cluster_name)"
      env="${image_tag%-*}"
      app="${CI_PROJECT_NAME}"
      tasks=$(aws ecs run-task --cluster "${cluster}" --overrides '{ "containerOverrides": [ { "name": "postgres", "command": ["/.gitlab-ci/scripts/db-create.sh"] } ] }' --task-definition "${app}-${env}-dynamic-task-definition" --region "${AWS_DEFAULT_REGION}" | jq -r ".tasks[].taskArn")
      echo $tasks
      aws ecs wait tasks-stopped --cluster "${cluster}" --tasks ${tasks}
    - sleep 15
    - ls -lah
    - ./db_migration.sh
    - pip install boto3
    - eval "$(assume-role.sh dev terraform)"
    - export TABLE_FROM=development-main-suzuka-digital-print-edition-issues-test
    - export TABLE_NAME=$(terraform output -raw dynamodb_name)
    - echo $TABLE_NAME
    - python3 ../restore_dynamodb_dynamic_env.py
    - export TABLE_FROM=development-emags-issues
    - export TABLE_NAME=$(terraform output -raw dynamodb_name_emags)
    - echo $TABLE_NAME
    - python3 ../restore_dynamodb_dynamic_env.py
    - echo $TF_VAR_django_suzuka_url
    # Update the list of environments after the deployment
    - workspaces=$(terraform workspace list | tail -n +2 | awk '{print $NF}' | sed '/^[[:space:]]*$/d')
    - file_name="env-list-$(date +%s)"
    - echo ${file_name}
    - |
      echo '<div class="alert alert-primary" role="alert">' >> ../${file_name}
      echo "<a class=\"link-success\" href=\"${CI_JOB_URL}\">Last updated</a> at: $(TZ=Australia/Sydney date) by ${GITLAB_USER_EMAIL}." >> ../${file_name}
      echo '</div>' >> ../${file_name}
      echo '<ul class="list-group list-group-flush">' >> ../${file_name}
      for workspace in ${workspaces}; do
        echo "<li class=\"list-group-item\">${workspace}</li>" >> ../${file_name}
      done
      echo "</ul>" >> ../${file_name}
  only:
    - branches
  artifacts:
    paths:
      - ./env-list*
    expire_in: 1 days
  except:
    - master
    - merge_requests
    - schedules
  when: manual
  allow_failure: false
  resource_group: ${CI_COMMIT_REF_NAME}

destroy environment: # workflow: branch commit
  stage: standalone
  before_script:
    - |
      ECR_REGISTRY_URL_DEV="${DEVELOPMENT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_CI="${CICD_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_STAGING="${STAGING_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_UAT="${UAT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
  script:
    - cat ~/.git-credentials || true
    - eval "$(assume-role.sh development terraform)"
    - git config --global user.name "acm-bot-pr"
    - git config --global user.email "<EMAIL>"
    - printf "https://gitlab-ci-token:%<EMAIL>\n" $CI_JOB_TOKEN > ~/.git-credentials
    - git config --global credential.helper 'store --file ~/.git-credentials'
    - git remote -v
    - git branch -av
    - git clone -b master https://gitlab.com/fairfax-acm/deployers/tf_suzuka_ecs_service.git
    - cd tf_${CI_PROJECT_NAME}_ecs_service
    - terraform init -backend-config=`pwd`/dynamic/backend-dynamic-main.conf -backend=true -lock=true --lock-timeout=120s
    - echo -e "workspaces to destroy:\n\n${workspaces}"
    - image_tag=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    - workspace="${workspace:-${image_tag%-*}}"
    - echo $workspace
    - export TF_VAR_monaco_host="http://monaco.app.newsnow.development"
    - |
      eval "$(assume-role.sh development terraform)"
      query_template='{":v1": {"S": "%s"}}'
      printf "$query_template" ${workspace} > attributes.json
      tf_commit_sha=$(aws dynamodb query --table-name $SUZUKA_TF_MAPPINGS_TABLE --key-condition-expression "branch_name = :v1" --expression-attribute-values file://attributes.json | jq -r '.Items | .[] | .sha.S')
      if [[ ! -z $tf_commit_sha ]]
      then
        git fetch --all
        git checkout $tf_commit_sha
      fi
      terraform workspace select ${workspace}
      terraform workspace list
      export TF_VAR_site=${CI_PROJECT_NAME}-${workspace}
      export TF_VAR_environment_name=${CI_PROJECT_NAME}-${workspace}
      export TF_VAR_django_environment=${workspace}
      export TF_VAR_django_management_host=-edit.${CI_PROJECT_NAME}-${workspace}.dev.newsnow.io
      export TF_VAR_django_suzuka_url=https://suzuka-${workspace}.dev.newsnow.io/
      export TF_VAR_app_image=racetracks/${CI_PROJECT_NAME}:${workspace}-${CI_PIPELINE_IID}
      export TF_VAR_environment=${workspace}
      export TF_VAR_pgdatabase_dynamic=${CI_PROJECT_NAME}_${workspace//[^A-z0-9_]/_}
      match_host_header="${CI_PROJECT_NAME}-${workspace}.dev.newsnow.io"
      env
      terraform refresh -var-file=`pwd`/dynamic/dynamic-main.tfvars
      terraform output -raw ecs_cluster_name > ecs_cluster_name
      terraform output -raw task_definition_family > task_definition 2>/dev/null
      terraform output -raw task_definition_dynamic_family > task_definition_dynamic 2>/dev/null
      cat ecs_cluster_name task_definition task_definition_dynamic 2>/dev/null
      cluster="$(< ecs_cluster_name)"
      env="${workspace}"
      app="${CI_PROJECT_NAME}"
      td="${app}-${env}-dynamic-task-definition"
      td_exists_status=0
      aws ecs describe-task-definition --task-definition ${td} || td_exists_status=$?
      if [ $td_exists_status -ne 0 ]; then 
        # This is just a dummy operation to activate the task definition again as there is not much support in ECS regarding inactive task definitions and this way we can reactivate the task definition
        aws ecs register-task-definition --family ${td} --container-definitions "[{\"name\":\"sleep\",\"image\":\"busybox\",\"cpu\":10,\"command\":[\"sleep\",\"360\"],\"memory\":10,\"essential\":true}]"
        # Now we can create the task definition again with the same containers
        container_definitions=$(aws ecs describe-task-definition --task-definition ${td}:1 | jq -r '.taskDefinition.containerDefinitions')
        aws ecs register-task-definition --family ${td} --container-definitions "$container_definitions"
        echo "registered task definition"
      fi
      echo "task definition to use: $td"
      tasks=$(aws ecs run-task --cluster "${cluster}" --overrides '{ "containerOverrides": [ { "name": "postgres", "command": ["/.gitlab-ci/scripts/db-drop.sh"] } ] }' --task-definition "${td}" --region "${AWS_DEFAULT_REGION}" | jq -r ".tasks[].taskArn")
      echo $tasks
      aws ecs wait tasks-stopped --cluster "${cluster}" --tasks ${tasks}
      sleep 20
      destroy_status=0
      terraform destroy -var-file=`pwd`/dynamic/dynamic-main.tfvars -auto-approve -lock=true --lock-timeout=120s 2>log || destroy_status=$?
      if [ $td_exists_status -ne 0 ]; then 
        # Terraform doesn't give details error code on errors, parsing the output to check if the output is added after the resource creation
        if  grep -q ".*output.*: Resource .* does not have attribute" ./log ; then
          echo "" > output.tf
          terraform destroy -var-file=`pwd`/dynamic/dynamic-main.tfvars -auto-approve -lock=true --lock-timeout=120s
        fi
        rm ./log
      fi
      key='{"branch_name": {"S": "%s"}}'
      printf "$key" ${workspace} > key.json
      aws dynamodb delete-item --table-name suzuka-dynamic-environment-TF-mappings --key file://key.json
      terraform workspace select default
      terraform workspace list
      terraform workspace delete ${workspace}
      cd ../
      ./.gitlab-ci/bootstrap.sh --func=clean-up --repo-name="${ECR_REGISTRY_URL_DEV##*/}/${CI_PROJECT_NAME}" --image-tag="${workspace}"
    - |
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
      eval "$(assume-role.sh staging terraform)"
      ./.gitlab-ci/bootstrap.sh --func=clean-up --repo-name="${ECR_REGISTRY_URL_STAGING##*/}/${CI_PROJECT_NAME}" --image-tag="${workspace}"
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
      eval "$(assume-role.sh uat terraform)"
      ./.gitlab-ci/bootstrap.sh --func=clean-up --repo-name="${ECR_REGISTRY_URL_UAT##*/}/${CI_PROJECT_NAME}" --image-tag="${workspace}"
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
      ./.gitlab-ci/bootstrap.sh --func=clean-up --repo-name="${ECR_REGISTRY_URL_CI##*/}/${CI_PROJECT_NAME}" --image-tag="${workspace}"
    - |
      cd -
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
    # find all the workspaces
    - eval "$(assume-role.sh development terraform)"
    - terraform init -backend-config=`pwd`/dynamic/backend-dynamic-main.conf -backend=true -lock=true --lock-timeout=120s
    - workspaces=$(terraform workspace list | tail -n +2 | awk '{print $NF}' | sed '/^[[:space:]]*$/d')
    - file_name="env-list-$(date +%s)"
    - echo ${file_name}
    - |
      echo '<div class="alert alert-primary" role="alert">' >> ../${file_name}
      echo "<a class=\"link-success\" href=\"${CI_JOB_URL}\">Last updated</a> at: $(TZ=Australia/Sydney date) by ${GITLAB_USER_EMAIL}." >> ../${file_name}
      echo '</div>' >> ../${file_name}
      echo '<ul class="list-group list-group-flush">' >> ../${file_name}
      for workspace in ${workspaces}; do
        echo "<li class=\"list-group-item\">${workspace}</li>" >> ../${file_name}
      done
      echo "</ul>" >> ../${file_name}
  timeout: 3h
  when: manual
  artifacts:
    paths:
      - ./env-list*
    expire_in: 1 days
  only:
    - branches
  except:
    - master
    - schedules
  resource_group: ${CI_COMMIT_REF_NAME}

build env list: # workflows: branch commit
  stage: standalone
  before_script:
    - |
      ECR_REGISTRY_URL_DEV="${DEVELOPMENT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_CI="${CICD_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_STAGING="${STAGING_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_UAT="${UAT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - image_tag=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    - |
      if [ "$(echo ${CI_MERGE_REQUEST_ID})" ]; then
        git config --global user.name "acm-bot-pr"
        git config --global user.email "<EMAIL>"
        git config --list
        git pull origin master || true
        image_tag=mr-${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
      fi
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan bitbucket.org >> ~/.ssh/known_hosts
    - cat ~/.ssh/known_hosts
  script:
    - cat ~/.git-credentials || true
    - eval "$(assume-role.sh development terraform)"
    - git config --global user.name "acm-bot-pr"
    - git config --global user.email "<EMAIL>"
    - printf "https://gitlab-ci-token:%<EMAIL>\n" $CI_JOB_TOKEN > ~/.git-credentials
    - git config --global credential.helper 'store --file ~/.git-credentials'
    - git remote -v
    - git branch -av
    - git clone -b master https://gitlab.com/fairfax-acm/deployers/tf_suzuka_ecs_service.git
    - cd tf_${CI_PROJECT_NAME}_ecs_service
    - terraform init -backend-config=`pwd`/dynamic/backend-dynamic-main.conf -backend=true -lock=true --lock-timeout=120s
    # find all the workspaces
    - workspaces=$(terraform workspace list | tail -n +2 | awk '{print $NF}' | sed '/^[[:space:]]*$/d')
    - file_name="env-list-$(date +%s)"
    - echo ${file_name}
    - |
      echo '<div class="alert alert-primary" role="alert">' >> ../${file_name}
      echo "<a class=\"link-success\" href=\"${CI_JOB_URL}\">Last updated</a> at: $(TZ=Australia/Sydney date) by ${GITLAB_USER_EMAIL}." >> ../${file_name}
      echo '</div>' >> ../${file_name}
      echo '<ul class="list-group list-group-flush">' >> ../${file_name}
      for workspace in ${workspaces}; do
        echo "<li class=\"list-group-item\">${workspace}</li>" >> ../${file_name}
      done
      echo "</ul>" >> ../${file_name}
  when: manual
  only:
    - branches
  artifacts:
    paths:
      - ./env-list*
    expire_in: 1 days
  except:
    - master
    - schedules

destroy:cron: &destroy # workflows: scheduled
  stage: cleanup
  variables:
    SHOULD_HIBERNATE: 'true'
    SUZUKA_TF_MAPPINGS_TABLE: suzuka-dynamic-environment-TF-mappings
  before_script:
    - |
      ECR_REGISTRY_URL_DEV="${DEVELOPMENT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_CI="${CICD_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_STAGING="${STAGING_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_UAT="${UAT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - image_tag=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    - |
      if [ "$(echo ${CI_MERGE_REQUEST_ID})" ]; then
        git config --global user.name "acm-bot-pr"
        git config --global user.email "<EMAIL>"
        git config --list
        git pull origin master || true
        image_tag=mr-${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
      fi
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan bitbucket.org >> ~/.ssh/known_hosts
    - cat ~/.ssh/known_hosts
  script:
    - cat ~/.git-credentials || true
    - eval "$(assume-role.sh development terraform)"
    - git config --global user.name "acm-bot-pr"
    - git config --global user.email "<EMAIL>"
    - printf "https://gitlab-ci-token:%<EMAIL>\n" $CI_JOB_TOKEN > ~/.git-credentials
    - git config --global credential.helper 'store --file ~/.git-credentials'
    - git remote -v
    - git branch -av
    - git clone -b master https://gitlab.com/fairfax-acm/deployers/tf_suzuka_ecs_service.git
    - cd tf_${CI_PROJECT_NAME}_ecs_service
    - terraform init -backend-config=`pwd`/dynamic/backend-dynamic-main.conf -backend=true -lock=true --lock-timeout=120s
    # find all the workspaces
    - workspaces=$(terraform workspace list | tail -n +2 | awk '{print $NF}' | sed '/^[[:space:]]*$/d')
    - echo -e "workspaces:\n\n${workspaces}"
    - cd -
    - git fetch --all
    # find all the branches
    - branches="$(git branch -a | awk '{print $1}' | grep -E "^remotes/origin/.+$" | sed -E "s/^remotes\/origin\///g" | grep -v -E "^master|HEAD$" | sed "s/[^A-z0-9]/-/g" | tr '[[:upper:]]' '[[:lower:]]')"
    - prs="$(echo "$branches" | sed -E "s/^(.+)$/mr-\1/g" | tr '[[:upper:]]' '[[:lower:]]')"
    - patterns="$(echo -e "${branches}\n${prs}" | sed -E "s/^(.+)$/^ *\1 *$/g" | tr -s '\n' '|' | rev | cut -f2- -d '|' | rev)"
    - |
      echo -e "patterns to keep (regex: ${patterns}):\n\n${branches}\n${prs}"
    - echo "Setting workspace"
    # find the workspaces whose branch doesn't exist
    - workspaces=$(echo "${workspaces}" | grep -v -E "${patterns}") &> /dev/null || true
    - echo -e "workspaces to destroy:\n\n${workspaces}"
    - cd -
    - |
      for workspace in ${workspaces}; do
        eval "$(assume-role.sh development terraform)"

        query_template='{":v1": {"S": "%s"}}'
        printf "$query_template" ${workspace} > attributes.json
        tf_commit_sha=$(aws dynamodb query --table-name $SUZUKA_TF_MAPPINGS_TABLE --key-condition-expression "branch_name = :v1" --expression-attribute-values file://attributes.json | jq -r '.Items | .[] | .sha.S')
        if [[ ! -z $tf_commit_sha ]]
        then
          git checkout $tf_commit_sha
        fi

        terraform workspace select ${workspace}
        terraform workspace list
        export TF_VAR_monaco_host="http://monaco.app.newsnow.development"
        export TF_VAR_site=${CI_PROJECT_NAME}-${workspace}
        export TF_VAR_environment_name=${CI_PROJECT_NAME}-${workspace}
        export TF_VAR_django_environment=${workspace}
        export TF_VAR_django_management_host=-edit.${CI_PROJECT_NAME}-${workspace}.dev.newsnow.io
        export TF_VAR_django_suzuka_url=https://suzuka-${workspace}.dev.newsnow.io/
        export TF_VAR_app_image=racetracks/${CI_PROJECT_NAME}:${workspace}-${CI_PIPELINE_IID}
        export TF_VAR_environment=${workspace}
        export TF_VAR_pgdatabase_dynamic=${CI_PROJECT_NAME}_${workspace//[^A-z0-9_]/_}
        match_host_header="${CI_PROJECT_NAME}-${workspace}.dev.newsnow.io"
        env
        terraform refresh -var-file=`pwd`/dynamic/dynamic-main.tfvars
        terraform output -raw ecs_cluster_name > ecs_cluster_name
        terraform output -raw task_definition_family > task_definition 2>/dev/null
        terraform output -raw task_definition_dynamic_family > task_definition_dynamic 2>/dev/null
        cat ecs_cluster_name task_definition task_definition_dynamic 2>/dev/null
        cluster="$(< ecs_cluster_name)"
        env="${workspace}"
        app="${CI_PROJECT_NAME}"
        td="${app}-${env}-dynamic-task-definition"
        td_exists_status=0
        aws ecs describe-task-definition --task-definition ${td} || td_exists_status=$?
        if [ $td_exists_status -ne 0 ]; then 
          # This is just a dummy operation to activate the task definition again as there is not much support in ECS regarding inactive task definitions and this way we can reactivate the task definition
          aws ecs register-task-definition --family ${td} --container-definitions "[{\"name\":\"sleep\",\"image\":\"busybox\",\"cpu\":10,\"command\":[\"sleep\",\"360\"],\"memory\":10,\"essential\":true}]"
          # Now we can create the task definition again with the same containers
          container_definitions=$(aws ecs describe-task-definition --task-definition ${td}:1 | jq -r '.taskDefinition.containerDefinitions')
          aws ecs register-task-definition --family ${td} --container-definitions "$container_definitions"
        fi
        echo "task definition to use: $td"
        tasks=$(aws ecs run-task --cluster "${cluster}" --overrides '{ "containerOverrides": [ { "name": "postgres", "command": ["/.gitlab-ci/scripts/db-drop.sh"] } ] }' --task-definition "${td}" --region "${AWS_DEFAULT_REGION}" | jq -r ".tasks[].taskArn")
        echo $tasks
        aws ecs wait tasks-stopped --cluster "${cluster}" --tasks ${tasks}
        sleep 20
        destroy_status=0
        terraform destroy -var-file=`pwd`/dynamic/dynamic-main.tfvars -auto-approve -lock=true --lock-timeout=120s 2>log || destroy_status=$?
        if [ $td_exists_status -ne 0 ]; then 
          # Terraform doesn't give details error code on errors, parsing the output to check if the output is added after the resource creation
          if  grep -q ".*output.*: Resource .* does not have attribute" ./log ; then
            echo "" > output.tf
            terraform destroy -var-file=`pwd`/dynamic/dynamic-main.tfvars -auto-approve -lock=true --lock-timeout=120s
          fi
          rm ./log
        fi
        key='{"branch_name": {"S": "%s"}}'
        printf "$key" ${workspace} > key.json
        aws dynamodb delete-item --table-name suzuka-dynamic-environment-TF-mappings --key file://key.json
        terraform workspace select default
        terraform workspace list
        terraform workspace delete ${workspace}
        cd ../
        ./.gitlab-ci/bootstrap.sh --func=clean-up --repo-name="${ECR_REGISTRY_URL_DEV##*/}/${CI_PROJECT_NAME}" --image-tag="${workspace}"
        cd -
      done
    - |
      for workspace in ${workspaces}; do
        unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
        eval "$(assume-role.sh staging terraform)"
        cd ../
        ./.gitlab-ci/bootstrap.sh --func=clean-up --repo-name="${ECR_REGISTRY_URL_STAGING##*/}/${CI_PROJECT_NAME}" --image-tag="${workspace}"
        cd -
      done
      for workspace in ${workspaces}; do
        unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
        eval "$(assume-role.sh uat terraform)"
        cd ../
        ./.gitlab-ci/bootstrap.sh --func=clean-up --repo-name="${ECR_REGISTRY_URL_UAT##*/}/${CI_PROJECT_NAME}" --image-tag="${workspace}"
        cd -
      done
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
    - |
      for workspace in ${workspaces}; do
        cd ../
        ./.gitlab-ci/bootstrap.sh --func=clean-up --repo-name="${ECR_REGISTRY_URL_CI##*/}/${CI_PROJECT_NAME}" --image-tag="${workspace}"
        cd -
      done
    - ls
    - pwd
    # find all the workspaces
    - eval "$(assume-role.sh development terraform)"
    - terraform init -backend-config=`pwd`/dynamic/backend-dynamic-main.conf -backend=true
    - workspaces=$(terraform workspace list | tail -n +2 | awk '{print $NF}' | sed '/^[[:space:]]*$/d')
    - |
      if [[ $SHOULD_HIBERNATE == "true" ]]; then
        echo "" > output.tf
        for workspace in ${workspaces}; do
          echo "destroying ${workspace}"
          terraform workspace select ${workspace}
          terraform workspace list
          export TF_VAR_monaco_host="http://monaco.app.newsnow.development"
          export TF_VAR_site=${CI_PROJECT_NAME}-${workspace}
          export TF_VAR_environment_name=${CI_PROJECT_NAME}-${workspace}
          export TF_VAR_django_environment=${workspace}
          export TF_VAR_django_management_host=-edit.${CI_PROJECT_NAME}-${workspace}.dev.newsnow.io
          export TF_VAR_django_suzuka_url=https://suzuka-${workspace}.dev.newsnow.io/
          export TF_VAR_app_image=racetracks/${CI_PROJECT_NAME}:${workspace}-${CI_PIPELINE_IID}
          export TF_VAR_environment=${workspace}
          export TF_VAR_pgdatabase_dynamic=${CI_PROJECT_NAME}_${workspace//[^A-z0-9_]/_}
          env
          terraform refresh -var-file=`pwd`/dynamic/dynamic-main.tfvars
          terraform destroy -var-file=`pwd`/dynamic/dynamic-main.tfvars --auto-approve || true
          terraform workspace select default
          terraform workspace list
          terraform workspace delete ${workspace}
        done
      fi
    - workspaces=$(terraform workspace list | tail -n +2 | awk '{print $NF}' | sed '/^[[:space:]]*$/d')
    - |
      file_name="env-list-$(date +%s)"
      echo ${file_name}
      echo '<div class="alert alert-primary" role="alert">' >> ../${file_name}
      echo "<a class=\"link-success\" href=\"${CI_JOB_URL}\">Last updated</a> at: $(TZ=Australia/Sydney date) by ${GITLAB_USER_EMAIL}." >> ../${file_name}
      echo '</div>' >> ../${file_name}
      echo '<ul class="list-group list-group-flush">' >> ../${file_name}
      for workspace in ${workspaces}; do
        echo "<li class=\"list-group-item\">${workspace}</li>" >> ../${file_name}
      done
      echo "</ul>" >> ../${file_name}
  timeout: 3h
  artifacts:
    paths:
      - ./env-list*
    expire_in: 1 days
  when: always
  only:
    - schedules
  except:
    - tags
  resource_group: ${CI_COMMIT_REF_NAME}

post_release_cleanup: # workflows: production release
  <<: *destroy
  variables:
    SHOULD_HIBERNATE: 'false'
    SUZUKA_TF_MAPPINGS_TABLE: suzuka-dynamic-environment-TF-mappings
  only:
    - master
  except:
    - tags
    - schedules
  resource_group: ${CI_COMMIT_REF_NAME}

test:unit: &unit_test # workflows: MR, production release
  image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/devops/pipeline-images:assume-role-259
  stage: test
  services:
    - ************.dkr.ecr.ap-southeast-2.amazonaws.com/devops/pipeline-images:postgres-suzuka-16.1-265
    - ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:24.0.4-dind
    - ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/memcached:alpine
  variables:
    DOCKER_HOST: tcp://localhost:2375
    DOCKER_TLS_CERTDIR: ''
    DOCKER_DRIVER: overlay2
    RACETRACK_USER: ${RACETRACK_USER}
    RACETRACK_PASSWORD: ${RACETRACK_PASSWORD}
    OAUTH_KEY: suzuka-key
    OAUTH_SECRET: ${OAUTH_SECRET}
    OAUTH_CLIENTS: longbeach
    POSTGRES_HOST_AUTH_METHOD: trust
    STACK_DOMAIN: suzuka.racetracks.docker
  before_script:
    - until docker info > /dev/null 2>&1; do echo -e "\033[32mPreparing dind service...\033[0m" && sleep 1; done
    - ECR_REGISTRY_URL="${DEVELOPMENT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - image_tag=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    - eval "$(assume-role.sh development terraform)"
    - docker version
    - docker images
    - |
      eval "cat <<EOF
      $(<.gitlab-ci/env.tpl)
      EOF
      " | tee .env
  script:
    - docker pull ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag}
    - docker run --net=host --env-file .env -t -v "$PWD:/artifacts" ${ECR_REGISTRY_URL}/${CI_PROJECT_NAME}:${image_tag} /bin/bash -c "ruff check . && ./manage.py makemigrations --check --dry-run && ./manage.py checkmigrations && coverage run --branch -m pytest --junitxml=/artifacts/report.xml && coverage report --show-missing && coverage html -d /artifacts/coverage && coverage xml -o /artifacts/coverage.xml && mypy ."
  only:
    - master
    - merge_requests
  except:
    - schedules
  artifacts:
    paths:
      - coverage/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
      junit: report.xml
  coverage: /(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/
  retry:
    max: 2
    when:
      - always
  allow_failure: false

test:unit:dynamic: # workflow: branch commit
  <<: *unit_test
  stage: deploy dynamic
  only:
    - branches
  except:
    - master
    - schedules

push image:production: # workflow: production release
  stage: push image
  image: ************.dkr.ecr.ap-southeast-2.amazonaws.com/devops/pipeline-images:assume-role-259
  services:
    - ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:24.0.4-dind
  variables:
    DOCKER_HOST: tcp://localhost:2375
    DOCKER_TLS_CERTDIR: ''
    DOCKER_DRIVER: overlay2
  before_script:
    - until docker info > /dev/null 2>&1; do echo -e "\033[32mPreparing dind service...\033[0m" && sleep 1; done
  script:
    # find the latest tag with the same pattern as the base
    - git fetch --tags
    - latest=$(((git tag --list | grep -E "^v[0-9]+$")||true) | sort -V -r | head -n 1) || true
    - echo $latest
    - if [ -z latest ]; then latest=${CI_PIPELINE_IID}; fi
    - echo $latest
    # increase the version by one
    - new_tag=$(echo $latest | awk -Fv '{printf("v%d", ++$2)}')
    - echo $new_tag
    - echo $new_tag > release_version
    # tag the HEAD
    - git remote add origin-write "https://${GITLAB_APP_USERNAME}:${GITLAB_APP_TOKEN}@gitlab.com/${CI_PROJECT_PATH}.git"
    - git tag ${new_tag}
    # push the tag
    - git push origin-write ${new_tag}
    - |
      ECR_REGISTRY_URL_DEV="${DEVELOPMENT_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
      ECR_REGISTRY_URL_PRODUCTION="${PROD_ACCOUNT}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - image_tag_commit_sha=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
    - image_tag_pipeline_id=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    - |
      eval "$(assume-role.sh development terraform)"
      docker pull ${ECR_REGISTRY_URL_DEV}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
      docker tag ${ECR_REGISTRY_URL_DEV}/${CI_PROJECT_NAME}:${image_tag_commit_sha} ${ECR_REGISTRY_URL_PRODUCTION}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker tag ${ECR_REGISTRY_URL_DEV}/${CI_PROJECT_NAME}:${image_tag_commit_sha} ${ECR_REGISTRY_URL_PRODUCTION}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      docker tag ${ECR_REGISTRY_URL_DEV}/${CI_PROJECT_NAME}:${image_tag_commit_sha} ${ECR_REGISTRY_URL_PRODUCTION}/${CI_PROJECT_NAME}:${new_tag}
      eval "$(assume-role.sh production terraform)"
      docker push ${ECR_REGISTRY_URL_PRODUCTION}/${CI_PROJECT_NAME}:${image_tag_commit_sha}
      docker push ${ECR_REGISTRY_URL_PRODUCTION}/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
      docker push ${ECR_REGISTRY_URL_PRODUCTION}/${CI_PROJECT_NAME}:${new_tag}
      docker images
  only:
    - master
  except:
    - schedules
  artifacts:
    paths:
      - release_version
  allow_failure: false

##### Deployments

deploy development: &deploy # workflow: MR
  stage: deploy
  variables:
    NEWRELIC_APP_ID: ${NEWRELIC_APP_ID_DEVELOPMENT}
    NEWRELIC_API_KEY_ENV: ${NEWRELIC_API_KEY_NONPROD}
    ac_num: ${DEVELOPMENT_ACCOUNT}
    ac: development
  environment:
    name: development
  script:
    - |
      [[ -f release_version ]] && export TF_VAR_release=$(cat release_version)
    - env
    - ECR_REGISTRY_URL="${ac_num}.dkr.ecr.ap-southeast-2.amazonaws.com/racetracks"
    - image_tag_commit_sha=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
    - image_tag_pipeline_id=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    - eval "$(assume-role.sh ${ac} terraform)"
    - PROJECT_NAME=${CI_PROJECT_NAME}
    - git config --global user.name "acm-bot-pr"
    - git config --global user.email "<EMAIL>"
    - printf "https://gitlab-ci-token:%<EMAIL>\n" $CI_JOB_TOKEN > ~/.git-credentials
    - git config --global credential.helper 'store --file ~/.git-credentials'
    - git remote -v
    - git branch -av
    - git clone -b master https://gitlab.com/fairfax-acm/deployers/tf_${CI_PROJECT_NAME}_ecs_service.git
    - cd tf_${CI_PROJECT_NAME}_ecs_service
    - git --version
    - terraform -v
    - export TF_VAR_app_image=racetracks/${CI_PROJECT_NAME}:${image_tag_pipeline_id}
    - terraform init -backend-config=`pwd`/${ac}/backend-${ac}-main.conf -lock=true -backend=true -lock-timeout=60s
    - terraform get --update=true
    - terraform plan -var-file=`pwd`/${ac}/${ac}-main.tfvars -out=plan -lock=true -lock-timeout=120s
    - terraform apply -lock=true --lock-timeout=120s plan
    - terraform output -raw ecs_cluster_name > ecs_cluster_name
    - terraform output -raw task_definition_family > task_definition
    - cat ecs_cluster_name task_definition
    - ls -lah
    - ./db_migration.sh
  after_script:
    - IMAGE_BUILD=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    - APP=$CI_PROJECT_NAME
    - COMMIT_MSG=$(echo $CI_COMMIT_MESSAGE | sed 's/"/\\"/g')
    - AUTHOR=$(git --no-pager show -s --format='%an' $CI_COMMIT_SHA| sed 's/"/\\"/g')
    - template='{ "deployment":{ "revision":"%s", "changelog":"%s", "description":"%s", "user":"%s" } }'
    - printf "$template" "$IMAGE_BUILD" "$COMMIT_MSG" "$COMMIT_MSG" "$AUTHOR" > deploymarker.json
    - curl -X POST "https://api.newrelic.com/v2/applications/$NEWRELIC_APP_ID/deployments.json" -H "X-Api-Key:$NEWRELIC_API_KEY_ENV" -i -H "Content-Type:application/json" -d @deploymarker.json
  only:
    - merge_requests
  except:
    - schedules
  artifacts:
    paths:
      - deploymarker.json
  when: manual
  resource_group: ${ac}

deploy development:master: # workflow: production release
  <<: *deploy
  stage: non-prod deploy
  only:
    - master
  when: manual
  allow_failure: true

deploy staging: # workflow: MR
  <<: *deploy
  variables:
    NEWRELIC_APP_ID: ${NEWRELIC_APP_ID_STAGING}
    NEWRELIC_API_KEY_ENV: ${NEWRELIC_API_KEY_NONPROD}
    ac_num: ${STAGING_ACCOUNT}
    ac: staging
  environment:
    name: staging
  only:
    - merge_requests

deploy staging:master: # workflow: production release
  <<: *deploy
  stage: non-prod deploy
  variables:
    NEWRELIC_APP_ID: ${NEWRELIC_APP_ID_STAGING}
    NEWRELIC_API_KEY_ENV: ${NEWRELIC_API_KEY_NONPROD}
    ac_num: ${STAGING_ACCOUNT}
    ac: staging
  environment:
    name: staging
  only:
    - master
  when: on_success
  allow_failure: false

deploy uat: # workflow: MR
  <<: *deploy
  variables:
    NEWRELIC_APP_ID: ${NEWRELIC_APP_ID_UAT}
    NEWRELIC_API_KEY_ENV: ${NEWRELIC_API_KEY_NONPROD}
    ac_num: ${UAT_ACCOUNT}
    ac: uat
  environment:
    name: uat

deploy uat:master: # workflow: production release
  <<: *deploy
  stage: non-prod deploy
  variables:
    NEWRELIC_APP_ID: ${NEWRELIC_APP_ID_UAT}
    NEWRELIC_API_KEY_ENV: ${NEWRELIC_API_KEY_NONPROD}
    ac_num: ${UAT_ACCOUNT}
    ac: uat
  environment:
    name: uat
  only:
    - master
  when: always
  allow_failure: false

deploy production: # workflow: production release
  <<: *deploy
  variables:
    NEWRELIC_APP_ID: $NEWRELIC_APP_ID_PROD
    NEWRELIC_API_KEY_ENV: $NEWRELIC_API_KEY_PROD
    NEWRELIC_INSIGHTS_API_KEY_ENV: ${NEWRELIC_INSIGHTS_API_KEY_PROD}
    NEWRELIC_ACC_ID: ${NEWRELIC_ACCOUNT_PROD}
    ac_num: ${PROD_ACCOUNT}
    ac: production
  after_script:
    - IMAGE_BUILD=${CI_COMMIT_REF_SLUG}-${CI_PIPELINE_IID}
    - APP=$CI_PROJECT_NAME
    - COMMIT_MSG=$(echo $CI_COMMIT_MESSAGE | sed 's/"/\\"/g')
    - AUTHOR=$(git --no-pager show -s --format='%an' $CI_COMMIT_SHA| sed 's/"/\\"/g')
    - template='{ "deployment":{ "revision":"%s", "changelog":"%s", "description":"%s", "user":"%s" } }'
    - printf "$template" "$IMAGE_BUILD" "$COMMIT_MSG" "$COMMIT_MSG" "$AUTHOR" > deploymarker.json
    - curl -X POST "https://api.newrelic.com/v2/applications/$NEWRELIC_APP_ID/deployments.json" -H "X-Api-Key:$NEWRELIC_API_KEY_ENV" -i -H "Content-Type:application/json" -d @deploymarker.json
    - eventtemplate='{"eventType":"Deployments","app":"%s","revision":"%s","changelog":"%s","environment":"%s","user":"%s"}'
    - printf "$eventtemplate" "$APP" "$IMAGE_BUILD" "$COMMIT_MSG" "$ac" "$AUTHOR" > deployevent.json
    - python3 -m json.tool < deployevent.json
    - curl -i -X POST -H "Content-Type:application/json" -H "X-Insert-Key:$NEWRELIC_INSIGHTS_API_KEY_ENV" -d @deployevent.json https://insights-collector.newrelic.com/v1/accounts/$NEWRELIC_ACC_ID/events
    - tag_name=$(git tag --points-at HEAD )
    - echo $tag_name
    - echo ${CI_PROJECT_ID}
    - message=$(git log -1 --pretty=%B)
    - template='{ "name":"%s", "tag_name":"%s", "description":"%s" }'
    - printf "$template" "$tag_name" "$tag_name" "$message" > release.json
    - curl -X POST ${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/releases -H "Content-Type:application/json" -H "PRIVATE-TOKEN:${GITLAB_APP_TOKEN}" -d @release.json
  environment:
    name: production
  when: manual # not necessary, just to emphasize!
  only:
    - master
  artifacts:
    paths:
      - deploymarker.json
      - deployevent.json
  allow_failure: false
  needs:
    - deploy uat:master

##### End deployments

release:
  image:
    name: getsentry/sentry-cli:1.58.0
    entrypoint: [""]
  stage: post deploy
  script:
    - release=$(cat release_version)
    - echo $release
    - export SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN
    - export SENTRY_ORG=$SENTRY_ORGANIZATION_SLUG
    - export SENTRY_PROJECT=$SENTRY_PROJECT_NAME
    - sentry-cli releases new $release
    - sentry-cli releases set-commits --auto $release
    - sentry-cli releases finalize $release
    - echo "Finalized release for $release"
  only:
    - master
  except:
    - schedules

##### Regression Tests

regression:dev0: &regression_manual # workflow: MR
  stage: regression
  image: mcr.microsoft.com/playwright:v1.38.1
  variables:
    en: dev
    siteIndex: 0
  script:
    - cd sanity-test/autumn-tests-suite
    - npm pkg delete scripts.prepare
    - npm ci --only-production --no-audit --no-fund
    - CI_COMMIT_REF_SLUG="" ENV=${en} SITE_INDEX=${siteIndex} npx playwright test -g '@smoke'
  when: manual
  allow_failure: true
  timeout: 15 minutes
  only:
    - merge_requests
  except:
    - schedules
  artifacts:
      when: always
      paths:
          - sanity-test/autumn-tests-suite/results.xml
      reports:
          junit: sanity-test/autumn-tests-suite/results.xml

regression:dev1: # workflow: MR
  <<: *regression_manual
  variables:
    en: dev
    siteIndex: 1

regression:dev2: # workflow: MR
  <<: *regression_manual
  variables:
    en: dev
    siteIndex: 2

regression:dynamic0: &regression_dynamic # workflow: branch commit
  <<: *regression_manual
  variables:
    en: dev
    siteIndex: 0
  script:
    - cd sanity-test/autumn-tests-suite
    - npm pkg delete scripts.prepare
    - npm ci --only-production --no-audit --no-fund
    - ENV=${en} SITE_INDEX=${siteIndex} npx playwright test -g '@smoke'
  only:
    - branches
  except:
    - master
    - schedules
  when: always

regression:dynamic1: # workflow: branch commit
  <<: *regression_dynamic
  variables:
    en: dev
    siteIndex: 1

regression:dynamic2: # workflow: branch commit
  <<: *regression_dynamic
  variables:
    en: dev
    siteIndex: 2

regression:uat0: # workflow: MR
  <<: *regression_manual
  variables:
    en: uat
    siteIndex: 0

regression:uat1: # workflow: MR
  <<: *regression_manual
  variables:
    en: uat
    siteIndex: 1

regression:uat2: # workflow: MR
  <<: *regression_manual
  variables:
    en: uat
    siteIndex: 2

regression:uat0:master: &regression_uat_manual # workflow: production release
  <<: *regression_manual
  stage: uat regression
  needs:
    - deploy uat:master
  variables:
    en: uat
    siteIndex: 0
  only:
    - master
  when: on_success
  except:
    - schedules

regression:uat1:master: # workflow: production release
  <<: *regression_uat_manual
  variables:
    en: uat
    siteIndex: 1

regression:uat2:master: # workflow: production release
  <<: *regression_uat_manual
  variables:
    en: uat
    siteIndex: 2

#### End Regression tests

pages:
  stage: publish
  image: alpine
  script:
    - ls -l
    - env_list=$(ls | sort -r | grep env-list | head -n 1)
    - echo ${env_list}
    - mv gitlab-pages public
    - cd public
    - |
      mv ../${env_list} env-list
      awk 'NR==FNR { a[n++]=$0; next } /CONTENT/ { for (i=0;i<n;++i) print a[i]; next }1' env-list env-list.html > tmp && mv tmp env-list.html
  artifacts:
    paths:
      - public
  when: manual
