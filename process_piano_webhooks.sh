#!/bin/bash

# Graceful shutdown: forward TERM/INT to the child and exit after the current iteration
shutdown=false
child_pid=""

check_shutdown() {
  if [[ "${shutdown}" == "true" ]]; then
    echo "SHELL: Shutdown requested - exiting with code 0"
    exit 0
  fi
}

term_handler() {
  local signal_name="${1:-UNKNOWN}"
  echo "SHELL: Received ${signal_name} signal - setting shutdown=true and forwarding to child process (PID: ${child_pid})"
  shutdown=true
  if [[ -n "${child_pid}" ]]; then
    echo "SHELL: Sending SIGTERM to Python process (PID: ${child_pid})"
    kill -TERM "${child_pid}" 2>/dev/null || true

    # After killing the process, wait for it to exit
    echo "SHELL: Waiting for child process to exit"
    wait "${child_pid}"
    echo "SHELL: Child process exited"
    child_pid=""
    check_shutdown
  else
    echo "SHELL: No child process to signal"
  fi
}

# Trap with signal name for better logging
trap 'term_handler SIGTERM' TERM
trap 'term_handler SIGINT' INT

interval="${1:-10}"
shift 1

echo "SHELL: Starting process_piano_webhooks with ${interval}s restart interval"

while :; do
  newrelic-admin run-python manage.py process_piano_webhooks "$@" &
  child_pid="$!"
  echo "SHELL: Python process started with PID: ${child_pid}"
  
  # Wait for the child process and capture its exit code
  wait "${child_pid}"
  exit_code=$?
  echo "SHELL: Python process (PID: ${child_pid}) exited with code: ${exit_code}"
  child_pid=""

  check_shutdown

  # Sleep, but remain interruptible
  echo "SHELL: Sleeping for ${interval}s before next iteration..."
  sleep "${interval}"
  
  check_shutdown
done
