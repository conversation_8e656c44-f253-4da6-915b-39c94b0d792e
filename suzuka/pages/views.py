import json

from calm_cache.decorators import cache_response
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
from django.http import (
    Http404,
    HttpResponse,
    HttpResponseNotFound,
    JsonResponse,
)
from django.shortcuts import redirect, render
from django.template import loader
from django.utils.cache import patch_response_headers
from django.utils.html import strip_tags
from django.utils.text import Truncator
from django.views import View
from django.views.decorators.http import require_http_methods

from suzuka.conf.sites import current_site
from suzuka.outages.utils import get_filtered_outages
from suzuka.pages.context_processor import (
    prepare_layout_context,
    render_context_response,
)
from suzuka.pages.editing.utils import has_edit_perms
from suzuka.pages.models import Page, external_url
from suzuka.pages.monaco import is_full_page_render, is_monaco_render
from suzuka.pages.settings import LAYOUTS_DIR
from suzuka.pages.templatetags.page_tags import dpe_published_dates
from suzuka.pages.utils import (
    cache_response_key,
    create_redirect_response,
    page_for_request,
    primary_secondary_pages,
    render_with_ad_data_layer,
)
from suzuka.stories.feeds.application import StoryFeed
from suzuka.stories.templatetags.story_tags import (
    story_url_for,
    with_images_at,
)

feed = StoryFeed(pinning=True)

DEFAULT_TIMEOUT = getattr(settings, "MENU_CACHE_DEFAULT_TIMEOUT", 1800)  # 30m


class RobotsView(View):
    def get(self, request, *args, **kwargs):
        site_settings = current_site().settings

        if (
            settings.ENVIRONMENT != "production"
            or not site_settings.web_crawlers
        ):
            # Ensure we disallow all crawlers
            return HttpResponse(
                "User-agent: *\nDisallow: /", content_type="text/plain"
            )

        # If we allow crawlers, then we render a matching suzuka page for it (if found).
        return page_detail(request, *args, **kwargs, url="robots.txt")


@cache_response(10, key_func=cache_response_key)
def page_not_found(request, exception, template_name="404.html"):
    context = {"request_path": request.path}
    site_settings = current_site().settings

    if is_monaco_render():
        context["full_page_render"] = True
        context["template_name"] = template_name
        response = render_context_response(request, context)

        if "location" in response:
            return response

        response.status_code = 404
        return response
    elif site_settings.features.adobetagmanagerfeature_enabled:
        response = render_with_ad_data_layer(request, template_name, context)
    else:
        response = render(request, template_name, context)

    return HttpResponseNotFound(response.content, content_type=None)


def page_feed(request, url):
    """
    Render a feed for the ``Page`` at the given URL.
    """
    page = page_for_request(request, url)
    return feed(request, pages=page)


@cache_response(10, key_func=cache_response_key, nocache_rsp=["Set-Cookie"])
def page_detail(request, url, template=None, view_type=None):
    """
    Main page view partially rendered by react server.

    """
    page = page_for_request(request, url)

    if page.redirect_to:
        redirect_to_url = (
            page.redirect_to
            if external_url(page.redirect_to)
            or page.redirect_to.startswith("/")
            else "/%s" % page.redirect_to
        )

        return redirect(redirect_to_url, permanent=True)

    site_settings = current_site().settings
    full_page_render = is_full_page_render()

    if page.url.endswith(".xml"):
        content_type = "application/xml"
    elif page.url.endswith(".txt"):
        content_type = "text/plain"
    else:
        content_type = None

    if not (is_monaco_render() and content_type is None):
        if template is None:
            template = "%s/%s" % (LAYOUTS_DIR, page.template)
        templates = ["%s/%s" % (site_settings.static_dir, template), template]
        template = loader.select_template(templates)

    if view_type is None:
        view_type = "homepage" if not url.strip("/") else "section"

    context = {
        "page": page,
        "view_type": view_type,
    }
    context.update(primary_secondary_pages(page, current_site()))

    if site_settings.use_suzuka_ui:
        if full_page_render and content_type is None:
            context["full_page_render"] = True
            return render_context_response(request, context)
        if view_type == "homepage":
            context = prepare_layout_context(request, context)
            if context.get("redirect"):
                return create_redirect_response(context)
    else:
        # Retrieve any relevant outage notifications for site
        context["outages"] = get_filtered_outages(page)

    if site_settings.features.adobetagmanagerfeature_enabled:
        return render_with_ad_data_layer(
            request, template.template.name, context, content_type=content_type
        )
    else:
        return render(
            request, template.template.name, context, content_type=content_type
        )


def _menu_items(tree, parent=None, ad_cats={}):
    """
    Return menu items from the tree for the given parent item.

    If `parent` is `None`, return items from the root of the tree.

    """
    items = []
    for node in tree[parent and parent.id]:
        name = getattr(node, "menu_name", None) or node.name
        item = {"name": name, "url": node.menu_url()}
        if node.story_list_id:
            # Fetch stories for the current node.
            item["stories"] = []
            story_list = node.story_list
            for story in with_images_at(story_list.stories(), "0,4,5")[:6]:
                item["stories"].append(
                    {
                        "url": story_url_for(
                            {"_story_list": story_list}, story
                        ),
                        "title": story.title,
                        "summary": Truncator(strip_tags(story.summary)).words(
                            18, truncate=" ..."
                        ),
                        "lead_image": story.lead_image
                        and {
                            "uri": story.lead_image["uri"],
                            "title": story.lead_image["title"],
                            "description": story.lead_image["description"],
                            "cropConfig": story.lead_image.get(
                                "cropConfig", {}
                            ),
                        },
                    }
                )
        if parent is None:  # Primary menu item.
            double_click_cat = ad_cats.get(node.url.strip("/"), None) or None
            item.update(
                {
                    "id": node.id,
                    "secondary": _menu_items(tree, parent=node),
                    "double_click_cat": double_click_cat,
                }
            )
        else:  # Secondary menu item.
            item["new_window"] = node.new_window
        items.append(item)
    return items


def allow_subdomain_origin(request):
    origin = request.META["HTTP_ORIGIN"]
    host = request.get_host()

    # strip off 'www.' from the host
    # e.g. www.example.com becomes example.com
    if host.startswith("www."):
        host = host[4:]

    allow_origin = []
    # if the origin ends with '.host' then allow it
    # e.g. foo.example.com, foo.bar.example.com would pass
    if origin.endswith("." + host):
        allow_origin.append(origin)
    return allow_origin


@require_http_methods(["GET"])
@cache_response(10)
def megamenu_feed(request):
    """
    Return megamenu hierarchy for the current site in JSON format.

    """
    menu_items = Page.current_site.menu_items().select_related("story_list")
    if not has_edit_perms(request):
        menu_items = menu_items.filter(draft=False)
    tree = menu_items.as_tree()

    # Retrieve ad categories for top-level menu items.
    ad_cats = {}
    for page in Page.current_site.filter(
        url__in=[node.url.strip("/") for node in tree[None]]
    ):
        ad_cats[page.url] = page.double_click_cat

    response = HttpResponse(
        json.dumps(
            _menu_items(tree, ad_cats=ad_cats),
            ensure_ascii=False,
            separators=(",", ":"),
            sort_keys=True,
        ),
        content_type="application/json; charset=utf-8",
    )
    patch_response_headers(response, cache_timeout=DEFAULT_TIMEOUT)
    return response


# @never_cache
def dpe_issues(request):
    dpe_id = request.GET.get("dpe_id")
    limit = request.GET.get("limit")
    start_date = request.GET.get("start_date")

    site = current_site()

    context = dict(
        conf=dict(
            dpefeature_dpe_id=site.settings.dpefeature_dpe_id,
            dpefeature_dpe_publish_time=site.settings.dpefeature_dpe_publish_time,
        )
    )

    return JsonResponse(
        dpe_published_dates(context, dpe_id, limit, start_date), safe=False
    )


class MobileAppAssociationView(View):
    """Base view for mobile app association endpoints."""

    app_id_attr = None

    def __init__(self):
        """Check the configuration is set."""
        if not self.app_id_attr:
            raise ImproperlyConfigured("app_id_attr must be set")

    def get(self, request):
        """Build the response."""
        site_settings = current_site().settings

        app_id = getattr(site_settings, self.app_id_attr)

        if not site_settings.features.mobileappfeature_enabled or not app_id:
            raise Http404

        data = self.get_data(app_id, site_settings)

        return JsonResponse(data, safe=False)

    def get_data(self, app_id, site_settings):
        """Abstract function to build the data."""
        raise NotImplementedError


class AppleAppSiteAssociation(MobileAppAssociationView):
    """iOS app association data."""

    app_id_attr = "mobileappfeature_ios_app_id"

    def get_data(self, app_id, site_settings):
        """Get the iOS data."""
        # https://gist.github.com/anhar/6d50c023f442fb2437e1
        return {
            "applinks": {
                "apps": [],
                "details": [
                    {
                        # iOS <= 12
                        "appID": app_id,
                        "paths": ["/story/*"],
                        # iOS 13+
                        "appIDs": [app_id],
                        "components": [
                            {"/": "/story/*"},
                        ],
                    }
                ],
            }
        }


class AssetLinksView(MobileAppAssociationView):
    """Android app association data."""

    app_id_attr = "mobileappfeature_google_play_id"

    def get_data(self, app_id, site_settings):
        """Get the app association data."""
        # https://developer.android.com/training/app-links/
        # verify-android-applinks
        return [
            {
                "relation": ["delegate_permission/common.handle_all_urls"],
                "target": {
                    "namespace": "android_app",
                    "package_name": app_id,
                    "sha256_cert_fingerprints": site_settings.mobileappfeature_android_fingerprints.split(
                        ",",
                    ),
                },
            }
        ]
