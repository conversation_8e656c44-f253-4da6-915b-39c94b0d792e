import mimetypes
import re
import string
from contextlib import suppress
from typing import Any, Optional
from urllib.parse import urlencode

from bs4 import BeautifulSoup
from django.conf import settings
from django.contrib.sites.models import Site
from django.http import Http404
from django.template.loader import get_template
from django.urls import reverse
from django.utils.cache import set_response_etag
from django.utils.encoding import iri_to_uri
from django.utils.feedgenerator import (
    Atom1Feed,
    Rss201rev2Feed as BaseRss201rev2Feed,
    get_tag_uri,
    rfc2822_date,
    rfc3339_date,
)
from django.utils.timezone import get_current_timezone

from suzuka.conf.sites import current_site, site_for_organization
from suzuka.pages.utils import (
    page_for_request,
    page_for_story,
    primary_secondary_pages,
)
from suzuka.stories.feeds.base import (
    BaseDPEFeed,
    BaseMostPopular,
    BaseStoryListFeed,
    StoryListFeed,
    filter_unprintable_chars,
)
from suzuka.stories.images import image_has_crop
from suzuka.stories.templatetags.story_tags import get_signpost
from suzuka.stories.utils import (
    get_story,
    get_story_kicker,
    get_story_slug,
    is_rev_exclusive_story,
)
from suzuka.targetlists.utils import get_site_list_ids


class Rss201rev2Feed(BaseRss201rev2Feed):
    def rss_attributes(self):
        rss_attributes = super().rss_attributes()
        rss_attributes.update(
            {
                "xmlns:content": "http://purl.org/rss/1.0/modules/content/",
            }
        )
        return rss_attributes

    def add_item_elements(self, handler, item):
        super().add_item_elements(handler, item)
        if "content" in item:
            v = item["content"]
            if isinstance(v, str):
                v = filter_unprintable_chars(v)
            handler.addQuickElement("content:encoded", v)


class SyndicationMixin:
    """
    Mixin to format a feed for external or end-user syndication clients.

    """

    def __init__(self, use_cache=True, **kwargs):
        kwargs.update({"ids_only": False})
        super().__init__(use_cache=use_cache, **kwargs)

    def item_link(self, item):
        return "%s?src=rss" % super().item_link(item)

    def _image_url(self, image):
        return self._transform_url(image, width=600, height=338, fit="crop")

    def item_enclosure_url(self, item):
        if not item.lead_image:
            return None
        return self._image_url(item.lead_image)

    def item_enclosure_mime_type(self, item):
        if not item.lead_image:
            return None

        mimetype, encoding = mimetypes.guess_type(item.lead_image["uri"])
        return mimetype or "image/jpeg"  # Preview images are usually JPEG.

    def item_enclosure_length(self, item):
        # Exact size is unknown. Preview images are about 15KB.
        return 15 * 1024


class DPERssFeed(Rss201rev2Feed):
    """
    Rss feed of a digital print edition.

    """

    # RSS tag -> item attribute
    custom_item_tags = {
        "icon": "icon",
    }

    def add_item_elements(self, handler, item):
        """Add custom item tags."""
        super().add_item_elements(handler, item)

        for tag_name, key in self.custom_item_tags.items():
            value = item[key]
            if not value:
                continue

            if not isinstance(value, list):
                value = (value,)

            for v in value:
                handler.addQuickElement(tag_name, v)


class DPESyndication(BaseDPEFeed):
    """
    feed for syndication of a digital print edition.

    """

    feed_type: Any = DPERssFeed

    def item_extra_kwargs(self, item):
        extra_kwargs = super().item_extra_kwargs(item)
        extra_kwargs.update({"icon": item["cover_img"]})
        return extra_kwargs


class PopulatedSyndicationMixin:
    """
    Fully-populated feed for syndication of a story list.

    """

    feed_type = Rss201rev2Feed

    def items(self, obj):
        # FIXME: This should be implemented more efficiently, but the
        # story API currently doesn't return content body for a list.
        for item in super().items(obj):
            yield get_story(item.id)

    def item_extra_kwargs(self, item):
        extra_kwargs = super().item_extra_kwargs(item)
        extra_kwargs.update(
            {
                "content": self.item_content(item, inline_images=True),
            }
        )
        return extra_kwargs


class AllStoriesSyndication(SyndicationMixin, BaseStoryListFeed):
    """
    Syndication for all stories on a site.

    Stories will be filtered by the site's target list subscriptions.

    """


class StoryListSyndication(SyndicationMixin, StoryListFeed):
    """
    Syndication for stories in a story list.

    """


class PageSyndication(StoryListSyndication):
    """
    Syndication for the main ``StoryList`` on a ``Page`` of the current site.

    """

    def __init__(self, **storylist_kwargs):
        super().__init__(**storylist_kwargs)
        self._cropped = True

    def get_feed(self, obj, request):
        self._cropped = request.GET.get("cropped", "true").lower() != "false"
        return super().get_feed(obj, request)

    def _image_url(self, image):
        if self._cropped:
            return self._transform_url(
                image, width=600, height=338, fit="crop"
            )

        # Choose the height based on the image's aspect ratio
        width = 600
        try:
            crop_config = image["cropConfig"]
            height = min(
                width * (crop_config["cropHeight"] / crop_config["cropWidth"]),
                3000,
            )
        except (KeyError, TypeError):
            height = 338

        return self._transform_url(
            image, width=width, height=height, fit="crop"
        )

    def get_object(self, request, url):
        page = page_for_request(request, url)

        if not page.story_list:
            # No main story list - try to find the first story list item on the
            # page
            zone_item = (
                page.zone_items.filter(
                    storylist__story_list__isnull=False,
                    element_type="storylist",
                )
                .select_related("storylist", "storylist__story_list")
                .first()
            )

            if not zone_item:
                raise Http404(f"Story list for page {url} not available.")

            return zone_item.storylist.story_list
        return page


class PopulatedPageSyndication(PopulatedSyndicationMixin, PageSyndication):  # type: ignore[misc]
    pass


class MostPopularSyndication(BaseMostPopular, StoryListSyndication):  # type: ignore[misc]
    pass


class MockRequest:
    """A mock request object for image URL generation."""

    def is_secure(self):
        """Always use HTTPS."""
        return True


class FullContentSyndication(PopulatedSyndicationMixin, PageSyndication):  # type: ignore[misc]
    """Base class for RSS feeds with full story content as HTML."""

    template_name = None

    def __init__(self, *args, **kwargs):
        """Cache the template and request instances."""
        super().__init__(*args, **kwargs)

        self.request = MockRequest()
        self.template = get_template(self.template_name)

    def item_content(self, item, inline_images=False):
        """Render the template."""
        context = {
            "brightcove_account_id": settings.DEFAULT_BRIGHTCOVE_ACCOUNT_ID,
            "brightcove_player_id": settings.DEFAULT_BRIGHTCOVE_PLAYER_ID,
            "brightcove_adfree_player_id": settings.ADFREE_BRIGHTCOVE_PLAYER_ID,
            "canonical_url": self.item_link(item),
            "debug": settings.DEBUG,
            "norkon_app_env": settings.NORKON_PUGPIG_APP_ENV,
            # Needed for image URL generation
            "request": self.request,
            "story": item,
        }
        page = page_for_story(item, None)
        context.update(primary_secondary_pages(page, current_site()))

        return self.template.render(context)

    def item_link(self, item):
        return iri_to_uri(
            "https://%s%s"
            % (
                current_site().domain,
                reverse(
                    "story_detail",
                    kwargs={
                        "story_id": item.id,
                        "story_slug": get_story_slug(item),
                    },
                ),
            )
        )


class FacebookPageSyndication(FullContentSyndication):
    """An RSS feed for Facebook Instant Articles."""

    template_name = "facebook-instant-article.html"

    def fix_iframe_size(self, size, is_width=False):
        """
        Fix iframe width/height attributes to suit Facebook's requirements.

        - Remove "px" suffix if present
        - Convert % widths into percentage of 960px (Facebook's maximum)
        """
        if size.isdigit():
            return size

        if size.endswith("px"):
            return size[:-2]

        if is_width and size.endswith("%"):
            try:
                size = int(size[:-1])
            except ValueError:
                pass
            else:
                return int(960 * (size / 100))

        return size

    def fix_iframe(self, code):
        """Fix the width and height attributes on iframe code."""
        soup = BeautifulSoup(code, "html.parser")

        for iframe in soup.find_all("iframe"):
            for attr in ("width", "height"):
                try:
                    value = iframe[attr]
                except KeyError:
                    pass
                else:
                    value = self.fix_iframe_size(value, attr == "width")
                    iframe[attr] = value

        return soup.encode_contents(formatter=None)

    def item_content(self, item, inline_images=False):
        """Render the stripped down FB Instant Article template."""
        for element in item.body["elements"]:
            if element["type"] == "generic" and element["service"] == "iframe":
                element["embed"] = self.fix_iframe(element["embed"])
            elif element["type"] == "heading":
                # Facebook does not support headings above H2
                element["level"] = min(int(element["level"]), 2)

        return super().item_content(item, inline_images)

    def item_link(self, item):
        """Build a story URL without the slug."""
        return iri_to_uri(
            "https://%s%s"
            % (
                current_site().domain,
                reverse(
                    "story_detail_no_slug",
                    kwargs={
                        "story_id": item.id,
                    },
                ),
            )
        )


class CustomItemsFeedMixin:
    """Custom additions to the RSS feed generator."""

    # RSS tag -> item attribute
    custom_item_tags = {
        "authorLink": "author_link",
        "updated": "updated",
    }

    def add_item_elements(self, handler, item):
        """Add custom item tags."""
        super().add_item_elements(handler, item)

        for tag_name, key in self.custom_item_tags.items():
            value = item[key]
            if not value:
                continue

            if isinstance(value, list) and "has_child" in value[0]:
                handler.startElement(tag_name, {})
                self.add_nested_element(handler, value)
                handler.endElement(tag_name)
                continue

            if not isinstance(value, list):
                value = (value,)

            for v in value:
                if isinstance(v, str):
                    v = filter_unprintable_chars(v)
                handler.addQuickElement(tag_name, v)

    def add_nested_element(self, handler, parent):
        for child in parent:
            if "tag_name" in child:
                tag_name = child["tag_name"]
                handler.startElement(tag_name, {})
                for k in child["keys"]:
                    v = child[k]
                    if isinstance(v, list) and "has_child" in v[0]:
                        self.add_nested_element(handler, v)
                        continue
                    if isinstance(v, str):
                        v = filter_unprintable_chars(v)
                    handler.addQuickElement(k, v)
                handler.endElement(tag_name)
            else:
                for k in child["keys"]:
                    v = child[k]
                    if isinstance(v, str):
                        v = filter_unprintable_chars(v)
                    handler.addQuickElement(k, v)


ABSOLUTE_URL_PATTERN = re.compile(r"^(https?://|(mailto|tel|sms):)")
CONTACT_URL_PATTERN = re.compile(r"^(https?://(mailto|tel|sms):)")


class MarketingRssFeed(CustomItemsFeedMixin, BaseRss201rev2Feed):
    """
    Rss feed for marketing.

    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # RSS tag -> item attribute
        self.custom_item_tags = {
            **self.custom_item_tags,
            "g:image_link": "image_link",
            "sections": "sections",
        }

    def root_attributes(self):
        attributes = super().root_attributes()
        attributes.pop("xml:lang", None)
        attributes.update(
            {
                "xmlns:g": "http://schemas.google.com/pcn/2020",
                "xmlns:sections": "http://www.w3.org/2005/Atom",
            }
        )
        return attributes


class MarketingSyndicationMixin:
    """
    Marketing feed for syndication of a story list.

    """

    feed_type = MarketingRssFeed

    def _image_url(self, image):
        if self._cropped:
            return self._transform_url(
                image, width=1200, height=676, fit="crop"
            )

        # Choose the height based on the image's aspect ratio
        width = 1200
        try:
            crop_config = image["cropConfig"]
            height = min(
                width * (crop_config["cropHeight"] / crop_config["cropWidth"]),
                3000,
            )
        except (KeyError, TypeError):
            height = 676

        return self._transform_url(
            image, width=width, height=height, fit="crop"
        )

    def item_extra_kwargs(self, item):
        extra_kwargs = super().item_extra_kwargs(item)
        extra_kwargs.update(
            {
                "image_link": (
                    self._image_url(item.lead_image)
                    if item.lead_image
                    else None
                ),
                "updated": rfc2822_date(extra_kwargs["updated"]),
                "sections": self.item_sections(item),
            }
        )
        return extra_kwargs

    def item_guid(self, item):
        """Generate the GUID based on the story ID and any issues."""
        return item["id"]

    def item_sections(self, obj):
        """
        Capture the tags in individual section element
        """
        if not (tags := getattr(obj, "tags", [])):
            return
        sections = []
        for tag in tags:
            sections.append(
                {
                    "has_child": True,
                    "keys": ["section"],
                    "section": tag,
                }
            )
        return sections


class MarketingSyndication(MarketingSyndicationMixin, StoryListFeed):  # type: ignore[misc]
    def __init__(self, **storylist_kwargs):
        super().__init__(**storylist_kwargs)
        self._cropped = True

    def get_feed(self, obj, request):
        self._cropped = request.GET.get("cropped", "true").lower() != "false"
        return super().get_feed(obj, request)

    def get_object(self, request, url):
        page = page_for_request(request, url)

        if not page.story_list:
            # No main story list - try to find the first story list item on the
            # page
            zone_item = (
                page.zone_items.filter(
                    storylist__story_list__isnull=False,
                    element_type="storylist",
                )
                .select_related("storylist", "storylist__story_list")
                .first()
            )

            if not zone_item:
                raise Http404(f"Story list for page {url} not available.")

            return zone_item.storylist.story_list
        return page


class StoryListSyndicationMixin:
    """Story list RSS feed."""

    iframe_element_types = {
        "iframe",
        "twitter-tweet",
        "instagram",
        "lb24liveblog",
    }

    def __init__(self, *args, **kwargs):
        """Allow pinning."""
        super().__init__(*args, **kwargs)

        self.storylist_kwargs["pinning"] = True

    def item_categories(self, obj):
        """
        Return the first signpost tag, without its prefix.

        If there are no signpost tags, return None.
        """
        signpost = get_signpost(obj)

        if signpost:
            # Convert from uppercase back to title case
            return (string.capwords(signpost["title"]),)

        return None

    def item_guid(self, item):
        """Generate the GUID based on the story ID"""
        return item["id"]

    def item_author_link(self, obj):
        """
        Include the author's profile picture.

        RSS feeds do not have a tag for this, so we use a custom `authorLink`
        tag.
        """
        try:
            if obj.authors_detail and obj.authors_detail[0].mugshot:
                return self._transform_url(
                    {
                        "uri": obj.authors_detail[0].mugshot,
                    },
                    width=100,
                    height=100,
                )
        except AttributeError:
            pass

        return None

    def item_extra_kwargs(self, item):
        """Include the item's updated date."""
        kwargs = super().item_extra_kwargs(item)
        kwargs["updated"] = rfc2822_date(kwargs["updated"])
        return kwargs

    def set_iframe_width(self, code):
        """Set iframes to 100% width."""
        soup = BeautifulSoup(code, "html.parser")

        for iframe in soup.find_all("iframe"):
            iframe["width"] = "100%"

        return str(soup)

    def is_facebook_video(self, element):
        """Determine whether an element contains a Facebook video."""
        return (
            element["type"] == "generic"
            and element["service"] == "facebook"
            and "facebook.com/plugins/video.php?" in element["embed"]
        )

    def replace_relative_links(self, code):
        """
        Turn relative links into absolute links.

        If the scheme is relative, use https.
        """
        soup = BeautifulSoup(code, "html.parser")
        domain = current_site().domain

        for anchor in soup.find_all("a"):
            try:
                href = anchor["href"]
            except KeyError:
                continue

            if href.startswith("//"):
                # Relative scheme
                anchor["href"] = f"https:{href}"
            elif not ABSOLUTE_URL_PATTERN.match(href):
                # Relative URL
                href = href.lstrip("/")
                anchor["href"] = f"https://{domain}/{href}"
            elif CONTACT_URL_PATTERN.match(href):
                anchor["href"] = re.sub("https://", "", href)

        return str(soup)

    def item_content(self, item, inline_images=False):
        """Process iframes and links."""
        for element in item.body["elements"]:
            if element["type"] == "generic" and (
                element["service"] in self.iframe_element_types
                or self.is_facebook_video(element)
            ):
                element["embed"] = self.set_iframe_width(element["embed"])
            elif element["type"] == "paragraph":
                element["text"] = self.replace_relative_links(element["text"])
            elif element["type"] == "image":
                link = element.get("link")
                if link:
                    element["link"] = self.replace_relative_links(link)
                    element["description"] = filter_unprintable_chars(
                        element.get("description", "")
                    )
            elif element["type"] == "gallery":
                for image in element["elements"]:
                    link = image.get("link")
                    if link:
                        image["link"] = self.replace_relative_links(link)
                        image["description"] = filter_unprintable_chars(
                            image.get("description", "")
                        )

        return super().item_content(item, inline_images)


class PugpigRssFeed(CustomItemsFeedMixin, Rss201rev2Feed):
    """Custom additions to the RSS feed generator for Pugpig."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # RSS tag -> item attribute
        self.custom_item_tags = {
            **self.custom_item_tags,
            "issue": "issues",
            "position": "position",
            "role": "role",
            "sections": "sections",
        }

    def root_attributes(self):
        attributes = super().root_attributes()
        attributes.update(
            {
                "xmlns:sections": "http://www.w3.org/2005/Atom",
            }
        )
        return attributes


class PugpigPageSyndication(StoryListSyndicationMixin, FullContentSyndication):
    """Story list RSS feed for Pugpig apps."""

    feed_type = PugpigRssFeed
    template_name = "pugpig-article.html"

    def __call__(self, *args, **kwargs):
        """
        Remove the Last-Modified header, and set ETag.

        This attempts to improve the cache refresh rate between Akamai and
        Pugpig.
        """
        response = super().__call__(*args, **kwargs)
        del response["last-modified"]

        return set_response_etag(response)

    def get_feed(self, obj, request):
        """Store the `issues` and `position_start` query string parameters."""
        self.issues = request.GET.getlist("issue")

        try:
            self.position_start = int(request.GET.get("position-start", 0))
        except ValueError:
            self.position_start = 0

        return super().get_feed(obj, request)

    def items(self, obj):
        """
        Add the story's position in the list.

        Pugpig's CMS orders by date but we need to support pinning.

        Multiple feeds go into one 'issue' we need to allow overriding the
        position to start from a certain number.

        REV stories shouldnt be included in PugPig XML
        as it will show broken page for our mobile readers.
        """
        position = self.position_start + self._offset + 1
        for item in super().items(obj):
            if is_rev_exclusive_story(item):
                continue
            item.position = position
            position += 1
            yield item

    def item_guid(self, item):
        """Generate the GUID based on the story ID and any issues."""
        parts = [item["id"]]
        parts.extend(self.issues)

        return "-".join(parts)

    def item_extra_kwargs(self, item):
        """Include the item's position, issues."""
        kwargs = super().item_extra_kwargs(item)
        kwargs["position"] = str(item.position)
        kwargs["issues"] = self.issues
        kwargs["role"] = self.item_author_role(item)
        kwargs["sections"] = self.item_sections(item)
        kwargs["comments"] = item.comments
        return kwargs

    def is_facebook_video(self, element):
        """Determine whether an element contains a Facebook video."""
        return (
            element["type"] == "generic"
            and element["service"] == "facebook"
            and "facebook.com/plugins/video.php?" in element["embed"]
        )

    def item_author_role(self, obj):
        """
        Return the author role
        """
        if not (authors_detail := getattr(obj, "authors_detail", None)):
            return
        with suppress(AttributeError, IndexError):
            author = authors_detail[0]
            if author.position:
                return author.position

    def item_sections(self, obj):
        """
        Capture the tags in individual section element
        """
        if not (tags := getattr(obj, "tags", [])):
            return
        sections = []
        for tag in tags:
            sections.append(
                {
                    "has_child": True,
                    "keys": ["section"],
                    "section": tag,
                }
            )
        return sections


class RevRSSFeed(CustomItemsFeedMixin, Rss201rev2Feed):
    """Custom additions to the RSS feed generator for REV."""

    # Attention!!!
    # Please, be aware of any changes in the class.
    # It is used in "BaseRev".
    # Any changes may impact "EBPearls" RSS Feed and vice-versa.

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # RSS tag -> item attribute
        self.custom_item_tags = {
            **self.custom_item_tags,
            "canonicalUrl": "canonical_url",
            "seoKeywords": "seo_keywords",
            "tags": "tags",
            "authors": "authors",
            "seoTitle": "seo_title",
            "shareWith": "share_with",
            "source": "source",
        }

    def root_attributes(self):
        attributes = super().root_attributes()
        attributes.update(
            {
                "xmlns:authors": "http://www.w3.org/2005/Atom",
                "xmlns:shareWith": "http://www.w3.org/2005/Atom",
            }
        )
        return attributes


class BaseRev(StoryListSyndicationMixin, FullContentSyndication):
    """Story list RSS feed."""

    # Attention!!!
    # Please, be aware of any changes in the base class.
    # It is derived by "EBPearls" and "Agsmarketplace".
    # Any changes may impact above RSS feeds and vice-versa.

    feed_type = RevRSSFeed
    ttl = 300  # 5 mins
    site_ids_map = {}

    def _get_site_list_ids(self, target_lists):
        key = ",".join(target_lists)
        if self.site_ids_map.get(key, None):
            site_list_ids = self.site_ids_map[key]
        else:
            site_list_ids = get_site_list_ids(target_lists)
            self.site_ids_map[key] = site_list_ids
        return site_list_ids

    def item_extra_kwargs(self, item):
        """Include the item's position, issues and updated date."""
        kwargs = super().item_extra_kwargs(item)
        kwargs["canonical_url"] = item.canonical_url
        kwargs["seo_keywords"] = ",".join(item.body.get("seo_keywords") or ())
        kwargs["tags"] = ",".join(item.tags)
        kwargs["authors"] = self.item_authors(item)
        kwargs["seo_title"] = self.item_seo_title(item)
        kwargs["share_with"] = self.item_shared_with(item)
        kwargs["source"] = self.item_source_org(item)
        return kwargs

    def item_author_link(self, obj):
        """
        Include the author's link url.
        """

        return None

    def item_authors(self, obj):
        """
        Include all the authors detail - id, name, email, photo and position.

        If byline is available, include it.
        """
        authors = None
        author_names = []
        if hasattr(obj, "authors_detail") and obj.authors_detail:
            for author in obj.authors_detail:
                if (
                    hasattr(author, "name")
                    and author.name
                    and hasattr(author, "id")
                    and author.id
                ):
                    author_info = {
                        "tag_name": "author",
                        "id": str(author.id),
                        "name": author.name,
                        "keys": ["id", "name"],
                        "has_child": True,
                    }
                    if hasattr(author, "position") and author.position:
                        author_info["keys"].append("position")
                        author_info["position"] = author.position
                    if hasattr(author, "email") and author.email:
                        author_info["keys"].append("email")
                        author_info["email"] = author.email
                    if hasattr(author, "mugshot") and author.mugshot:
                        author_info["keys"].append("photoUrl")
                        author_info["photoUrl"] = self._transform_url(
                            {
                                "uri": author.mugshot,
                            },
                            width=100,
                            height=100,
                        )
                    self.add_author_bio(author_info, author)
                    self.add_author_social_links(author_info, author)
                    authors = [] if not authors else authors
                    author_names.append(author.name)
                    authors.append(author_info)

        if hasattr(obj, "byline") and obj.byline:
            byline = obj.byline
            # Removing the duplicate names of author from byline
            for name in author_names:
                byline = byline.replace(name, "").strip()
            # Clean up for a case where
            # author name is followed by other byline text
            # this introduces "," between author name and byline text
            if byline and byline.startswith(","):
                byline = byline[1:].strip()
            if byline:
                authors = [] if not authors else authors
                authors.append(
                    {
                        "has_child": True,
                        "keys": ["byline"],
                        "byline": byline,
                    }
                )

        return authors

    def item_shared_with(self, obj):
        if not (hasattr(obj, "target_lists") and obj.target_lists):
            return None

        site_list_ids = self._get_site_list_ids(obj.target_lists)
        if not site_list_ids:
            return None

        return [
            {
                "tag_name": "site",
                "has_child": True,
                "keys": ["id", "name"],
                "id": str(site.id),
                "name": site.name,
            }
            for site in Site.objects.filter(
                settings__visible=True,
                settings__theme_dir__in=["legolite", "autumn"],
                id__in=site_list_ids,
            ).iterator()
        ]

    def item_seo_title(self, item):
        if item.body.get("title_seo"):
            return item.body.get("title_seo", "").strip()
        return None

    def add_author_bio(self, author_info, author):
        pass

    def add_author_social_links(self, author_info, author):
        pass

    def item_source_org(self, item):
        pass


class RevPageSyndication(BaseRev):
    """Story list RSS feed for Rev."""

    template_name = "rev-article.html"
    realestateview_site_id = int(settings.REALESTATE_VIEW_SITE_ID)

    def item_shared_with(self, obj):
        if not (hasattr(obj, "target_lists") and obj.target_lists):
            return None

        site_list_ids = self._get_site_list_ids(obj.target_lists)
        if not site_list_ids:
            return None

        # Real Estate View is excluded from shareWith
        site_list_ids = filter(
            lambda x: x != self.realestateview_site_id, site_list_ids
        )
        return [
            {
                "tag_name": "site",
                "has_child": True,
                "keys": ["id", "name"],
                "id": str(site.id),
                "name": site.name,
            }
            for site in Site.objects.filter(
                settings__visible=True,
                settings__theme_dir__in=["legolite", "autumn"],
                id__in=site_list_ids,
            ).iterator()
        ]

    def add_author_bio(self, author_info, author):
        if hasattr(author, "bio") and author.bio:
            author_info["keys"].append("bio")
            author_info["bio"] = author.bio

    def add_author_social_links(self, author_info, author):
        if hasattr(author, "instagram") and author.instagram:
            author_info["keys"].append("instagram")
            author_info["instagram"] = author.instagram
        if hasattr(author, "pinterest") and author.pinterest:
            author_info["keys"].append("pinterest")
            author_info["pinterest"] = author.pinterest
        if hasattr(author, "twitter") and author.twitter:
            author_info["keys"].append("twitter")
            author_info["twitter"] = author.twitter
        if hasattr(author, "youtube") and author.youtube:
            author_info["keys"].append("youtube")
            author_info["youtube"] = author.youtube


class GoogleNewsRundownAtom1Feed(Atom1Feed):
    def root_attributes(self):
        attributes = super().root_attributes()
        attributes.pop("xml:lang", None)
        attributes.update(
            {
                "xmlns:media": "http://search.yahoo.com/mrss/",
                "xmlns:g": "http://schemas.google.com/pcn/2020",
            }
        )
        return attributes

    def add_root_elements(self, handler):
        handler.addQuickElement("title", self.feed["title"])
        if self.feed["feed_url"] is not None:
            handler.addQuickElement(
                "link", "", {"rel": "self", "href": self.feed["feed_url"]}
            )
        handler.addQuickElement("id", self.feed["id"])
        handler.addQuickElement(
            "updated", rfc3339_date(self.latest_post_date())
        )

    def write_items(self, handler):
        last_updated = rfc3339_date(self.latest_post_date())
        handler.startElement("entry", {})
        handler.addQuickElement("id", f"rundown-{last_updated}")
        handler.addQuickElement("updated", last_updated)
        handler.addQuickElement(
            "g:panel", "Rundown Panel", {"type": "RUNDOWN"}
        )
        handler.addQuickElement("g:panel_title", self.feed.get("title", ""))
        handler.startElement("g:article_group", {"role": "RUNDOWN"})
        super().write_items(handler)
        handler.endElement("g:article_group")
        handler.endElement("entry")

    def add_item_elements(self, handler, item):
        handler.addQuickElement("title", item["title"])
        handler.addQuickElement("link", "", {"href": item["link"]})
        if item["pubdate"] is not None:
            handler.addQuickElement("published", rfc3339_date(item["pubdate"]))
        if item["updateddate"] is not None:
            handler.addQuickElement(
                "updated", rfc3339_date(item["updateddate"])
            )
        # Unique ID.
        if item["unique_id"] is not None:
            unique_id = item["unique_id"]
        else:
            unique_id = get_tag_uri(item["link"], item["pubdate"])
        handler.addQuickElement("id", unique_id)
        lead_image = item.get("lead_image")
        if lead_image:
            handler.addQuickElement("media:content", "", {"url": lead_image})
        handler.addQuickElement("g:overline", item.get("kicker"))


class GoogleNewsRundownSyndication(PageSyndication):
    feed_type = GoogleNewsRundownAtom1Feed

    def title(self, obj):
        return "Top News"

    def _image_url(self, image):
        if self._cropped:
            return self._transform_url(
                image, width=1200, height=600, fit="crop"
            )

    def item_extra_kwargs(self, item):
        extra_kwargs = super().item_extra_kwargs(item)
        extra_kwargs["lead_image"] = (
            self._image_url(item.lead_image) if item.lead_image else None
        )
        extra_kwargs["kicker"] = get_story_kicker(item.tags)
        return extra_kwargs

    def item_link(self, item):
        return iri_to_uri(
            "https://%s%s"
            % (
                current_site().domain,
                reverse(
                    "story_detail",
                    kwargs={
                        "story_id": item.id,
                        "story_slug": get_story_slug(item),
                    },
                ),
            )
        )

    def item_pubdate(self, item):
        return item.publish_from.astimezone(get_current_timezone())

    def item_updateddate(self, item):
        return item.updated_on.astimezone(get_current_timezone())

    def item_author_name(self, item):
        return

    def item_enclosure_url(self, item):
        return

    def item_description(self, item):
        return

    def feed_copyright(self):
        return


class EBPearlsPageSyndication(BaseRev):
    """Story list RSS feed for EBPearls."""

    template_name = "ebpearls-article.html"

    def resize_image(
        self, orig_width: int, orig_height: int
    ) -> tuple[int, int]:
        aspect_ratio = orig_width / orig_height
        max_width, max_height = 800, 600
        new_width, new_height = orig_width, orig_height

        if new_width > max_width:
            new_width = max_width
            new_height = round(new_width / aspect_ratio)

        if new_height > max_height:
            new_height = max_height
            new_width = round(new_height * aspect_ratio)

        return (new_width, new_height)

    def update_image_crop_config(
        self,
        orig_width: int,
        orig_height: int,
        new_width: int,
        new_height: int,
        crop_config: dict,
    ) -> Optional[dict]:
        """
        Update crop config data as per resized image
        """
        try:
            w_ratio = orig_width / new_width
            h_ratio = orig_height / new_height
            cropWidth = round((crop_config["cropWidth"] / w_ratio), 2)
            cropHeight = round((crop_config["cropHeight"] / h_ratio), 2)
            cropX = round((crop_config["cropX"] / w_ratio), 3)
            cropY = round((crop_config["cropY"] / h_ratio), 3)
            focalX = round((crop_config["focalX"] / w_ratio), 3)
            focalY = round((crop_config["focalY"] / h_ratio), 3)
        except ArithmeticError:
            return None

        # update crop width and crop height in case
        # it's greater than crop window
        cropHeight = (
            cropHeight
            if (new_height - cropY) > cropHeight
            else (new_height - cropY)
        )
        cropWidth = (
            cropWidth
            if (new_width - cropX) > cropWidth
            else (new_width - cropX)
        )

        return {
            "cropX": cropX,
            "cropY": cropY,
            "cropWidth": cropWidth,
            "cropHeight": cropHeight,
            "focalX": focalX,
            "focalY": focalY,
            "scale": 1,
        }

    def _image_cropconfig_url(self, image: dict) -> Optional[str]:
        """
        Return a resized transform url with image crop config and
        focal data as params
        """
        orig_width, orig_height = image["width"], image["height"]

        if orig_height == 0 or orig_width == 0:
            return None

        new_width, new_height = self.resize_image(orig_width, orig_height)
        uri = image["uri"].strip("/") if image["uri"] else ""
        transform_host = self._transform_host()
        if not uri or not transform_host:
            return None

        crop_config = None
        if image_has_crop(image):
            original_crop_config = image["cropConfig"]
            crop_config = self.update_image_crop_config(
                orig_width,
                orig_height,
                new_width,
                new_height,
                original_crop_config,
            )

        transform_base = f"transform/v1/resize/frm/{uri}/w{new_width}_h{new_height}_fcrop.jpg"
        # Return resized image with updated crop config if original image
        # has crop config data else return only resized image
        url = (
            f"{transform_host}{transform_base}?{urlencode(crop_config)}"
            if crop_config
            else f"{transform_host}{transform_base}"
        )

        return url

    # Return resized image with updated crop config data
    # EbPearls will take care of transformation from their end
    def item_enclosure_url(self, item):
        return (
            self._image_cropconfig_url(item.lead_image)
            if item.lead_image
            else None
        )


class AgsMarketPlacePageSyndication(EBPearlsPageSyndication):
    template_name = "agsmarketplace-article.html"

    def item_source_org(self, obj):
        if org := getattr(obj, "organization", None):
            if source_org := site_for_organization(org):
                url = iri_to_uri(
                    "https://%s%s"
                    % (
                        source_org.domain,
                        reverse(
                            "story_detail",
                            kwargs={
                                "story_id": obj.id,
                                "story_slug": get_story_slug(obj),
                            },
                        ),
                    )
                )

                return [
                    {
                        "has_child": True,
                        "keys": ["id", "name", "url"],
                        "id": str(source_org.id),
                        "name": source_org.name,
                        "url": url,
                    }
                ]

        return None
