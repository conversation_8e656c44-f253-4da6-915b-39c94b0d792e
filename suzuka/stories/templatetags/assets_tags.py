import json
import re

from django import template
from django.conf import settings

from suzuka.comments.views import ViafouraLiveblogSyndicationSignature
from suzuka.conf.sites import current_site

bc_video_id_re = re.compile(r"^[0-9]+$")
bc_video_embed_re = re.compile(r'id="myExperience([0-9]+)"')
dm_video_playlist_re = re.compile(
    r"https:\/\/(?:www\.)?dailymotion\.com\/embed\/(video|playlist)\/([a-zA-Z0-9]+)|https:\/\/geo\.dailymotion\.com\/player(?:\/[a-zA-Z0-9]+)?\.html\?(video|playlist)=([a-zA-Z0-9]+)",
    re.IGNORECASE,
)
register = template.Library()


@register.inclusion_tag("stories/brightcove.html", takes_context=True)
def brightcove_video(context, element):
    """
    Parses a Brightcove video element.

    """

    if (
        not isinstance(element, dict)
        or element.get("type") != "generic"
        or element.get("service_type") != "video"
        or element.get("service") != "brightcove"
    ):
        # Not a valid Brightcove video element.
        return ""

    brightcove_updates = element.get("brightcove", {})
    if not isinstance(brightcove_updates, dict):
        brightcove_updates = {}
    brightcove_updates.update(
        {
            "video_id": str(brightcove_updates.get("video_id") or "").strip()
            or str(element.get("service_id") or "").strip(),
            "account_id": (
                (brightcove_updates.get("account_id") or "").strip()
                or settings.DEFAULT_BRIGHTCOVE_ACCOUNT_ID
            ),
            "player_id": (
                (brightcove_updates.get("player_id") or "").strip()
                or (
                    context["conf"].get("brightcovefeature_player_id") or ""
                ).strip()
                or settings.DEFAULT_BRIGHTCOVE_PLAYER_ID
            ),
        }
    )

    if not bc_video_id_re.match(brightcove_updates["video_id"]):
        match = bc_video_embed_re.search(element.get("embed") or "")
        if not match:
            # Brightcove video ID not found.
            return ""
        brightcove_updates["video_id"] = match.group(1)

    render_context = context
    render_context.update(brightcove_updates)

    return render_context


def is_dailymotion_element(element):
    return (
        isinstance(element, dict)
        and element.get("type") == "generic"
        and element.get("service_type") == "video"
        and element.get("service") == "dailymotion"
    )


def extract_video_playlist(element):
    embed = str(element.get("embed") or "").strip()
    match = dm_video_playlist_re.search(embed)
    if not match:
        return False, None, None
    video_or_playlist = match.group(1) or match.group(3)
    video_or_playlist_id = match.group(2) or match.group(4)
    return True, video_or_playlist, video_or_playlist_id


@register.inclusion_tag("stories/dailymotion.html", takes_context=True)
def dailymotion_video(context, element):
    """
    Parses a Dailymotion video element.

    """

    if not is_dailymotion_element(element):
        return {}

    dailymotion_updates = element.get("dailymotion", {})
    if not isinstance(dailymotion_updates, dict):
        dailymotion_updates = {}
    dailymotion_updates.update(
        {
            "player_id": (
                (dailymotion_updates.get("player_id") or "").strip()
                or (
                    context["conf"].get("dailymotionfeature_player_id") or ""
                ).strip()
                or settings.DEFAULT_DAILYMOTION_PLAYER_ID
            ),
            "syndication_key": settings.DAILYMOTION_SYNDICATION_KEY,
        }
    )
    match, video_or_playlist, video_or_playlist_id = extract_video_playlist(
        element
    )
    if not match:
        return {}
    dailymotion_updates["video_or_playlist"] = video_or_playlist
    dailymotion_updates["video_or_playlist_id"] = video_or_playlist_id

    return dailymotion_updates


@register.inclusion_tag("dailymotion-iframe.html", takes_context=False)
def dailymotion_video_iframe(
    element, include_syndication_key=False, include_app_player=False
):
    """
    Parses a Dailymotion video element.

    """

    if not is_dailymotion_element(element):
        return {}

    dailymotion_updates = element.get("dailymotion", {})
    if not isinstance(dailymotion_updates, dict):
        dailymotion_updates = {}
    match, video_or_playlist, video_or_playlist_id = extract_video_playlist(
        element
    )
    if not match:
        return {}
    dailymotion_updates["video_or_playlist"] = video_or_playlist
    dailymotion_updates["video_or_playlist_id"] = video_or_playlist_id
    if include_syndication_key:
        dailymotion_updates["syndication_key"] = (
            settings.DAILYMOTION_SYNDICATION_KEY
        )
    if include_app_player:
        dailymotion_updates["player_id"] = (
            settings.DEFAULT_DAILYMOTION_PLAYER_ID_FOR_APP
        )

    return dailymotion_updates


@register.inclusion_tag("viafoura_liveblog_widget.html", takes_context=False)
def viafoura_liveblog_widget(request, story_org, story_id):
    """
    Return Viafoura Liveblog widget code. Syndication is True only
    if story original organization is different from the site it's
    being rendered on.

    """

    vf_blog_updates = {"use_syndication": False}
    site_settings = current_site().settings
    if site_settings.organization != story_org:
        response = ViafouraLiveblogSyndicationSignature().get(
            request, story_id
        )
        if response.status_code == 200:
            content = json.loads(response.content.decode("utf-8"))
            vf_blog_updates["use_syndication"] = True
            vf_blog_updates["signature"] = content.get("signature", "")

    return vf_blog_updates


def get_norkon_liveblog_context(element, app_env=""):
    site_settings = current_site().settings

    if not site_settings.features.norkonliveblogfeature_enabled:
        return {"norkon_liveblog_enabled": False}

    norkon_blog_updates = {"norkon_liveblog_enabled": True}
    asset_version = site_settings.norkonliveblogfeature_asset_version
    api_url = settings.NORKON_API_URL
    base_url = settings.NORKON_BASE_URL
    script_url = settings.NORKON_SCRIPT_URL
    websocket_url = settings.NORKON_WEBSOCKET_URL
    tenant_key = settings.NORKON_TENANT_KEY

    base_css_url = f"{base_url}scripts/ncposts/ncposts-{asset_version}.min.css"
    extension_css_url = f"{base_url}LiveCenter/ExtensionCss/{tenant_key}"
    base_js_url = f"{script_url}scripts/ncposts/ncposts-{asset_version}.min.js"
    base_js_url = f"{script_url}scripts/ncposts/ncposts-{asset_version}.min.js"
    extension_js_url = f"{script_url}LiveCenter/ExtensionJs/{tenant_key}"

    norkon_blog_updates.update(
        {
            "app_env": app_env,
            "api_url": api_url,
            "base_url": base_url,
            "base_css_url": base_css_url,
            "base_js_url": base_js_url,
            "extension_css_url": extension_css_url,
            "extension_js_url": extension_js_url,
            "service_id": element["service_id"],
            "tenant_key": tenant_key,
            "websocket_url": websocket_url,
        }
    )
    return norkon_blog_updates


@register.inclusion_tag(
    "norkon_liveblog_webview_widget.html", takes_context=False
)
def norkon_liveblog_webview_widget(request, element, app_env=""):
    """
    Return Norkon Liveblog webview widget code.
    """
    return get_norkon_liveblog_context(element, app_env)


@register.inclusion_tag(
    "norkon_liveblog_native_app_widget.html", takes_context=False
)
def norkon_liveblog_native_app_widget(request, element):
    """
    Return Norkon Liveblog native app widget code.
    """
    return get_norkon_liveblog_context(element)
