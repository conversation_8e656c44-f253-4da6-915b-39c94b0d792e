import importlib.util
import os
import platform
import socket
from typing import Any, Union
from urllib.parse import urlparse

import django_stubs_ext
import sentry_sdk
from corsheaders.defaults import default_headers
from decouple import AutoConfig, Csv, UndefinedValueError
from django.db import OperationalError
from django.urls import reverse_lazy
from sentry_sdk.integrations.django import DjangoIntegration

# Monkeypatching Django, so stubs will work for all generics,
# see: https://github.com/typeddjango/django-stubs
django_stubs_ext.monkeypatch()

# Used for coersion of string environment variables to python types. Note that
# there doesn't have to be any config files in the search path. However if an
# `.env` file is present, it will take precedence over environment variables.
env = AutoConfig(search_path=os.environ["BUILD_CONFIG_PATH"])

PROJECT_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

DEPLOY_TIMESTAMP = os.environ.get("DEPLOY_TIMESTAMP", "")
if not DEPLOY_TIMESTAMP and "BUILD_TIMESTAMP_FILE" in os.environ:
    DEPLOY_TIMESTAMP = (
        open(os.environ["BUILD_TIMESTAMP_FILE"], "r").readline().strip()
    )
ENVIRONMENT = env("DJANGO_ENVIRONMENT", default="development")


# Apps

INSTALLED_APPS: tuple[str, ...] = (
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.sites",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.sitemaps",
    "django.contrib.admin",
    "django.contrib.admindocs",
    "django.contrib.humanize",
    "dj_pagination",
    "endless_pagination",
    "form_utils",
    "crispy_forms",
    # Must be placed before pitcrews.layouts for template overrides
    "newsnow_cognito.common",
    "newsnow_cognito.consumer",
    "pitcrews.layouts",
    "pitcrews_health",
    "rest_framework",
    "jsonify",
    "reversion",
    "reversion_compare",
    "solo",
    "valencia_storage",
    # Suzuka features
    "suzuka.ab_testing",
    "suzuka.business",
    "suzuka.tvguide",
    "suzuka.partners",
    "suzuka.classifieds",
    "suzuka.ugc",
    "suzuka.ads",
    "suzuka.countrycars",
    "suzuka.jobs",
    "suzuka.brightcove",
    "suzuka.comments",
    "suzuka.segment",
    "suzuka.hotjar",
    "suzuka.real_estate",
    "suzuka.localads",
    "suzuka.subscriptions",
    "suzuka.mailinglists",
    "suzuka.sports_hub",
    "suzuka.sports_results",
    "suzuka.notification",
    "suzuka.global_features",
    "suzuka.auctions",
    "suzuka.clearing_sales",
    "suzuka.emags",
    "suzuka.dailymotion",
    "suzuka.piano",
    "suzuka.farmbuy",
    # Suzuka core
    "suzuka.builder",
    "suzuka.pages",
    "suzuka.menu",
    "suzuka.conf",
    "suzuka.elements_demo",
    "suzuka.stories",
    "suzuka.targetlists",
    "suzuka.authors",
    "suzuka.absolute_uri",
    "suzuka.outages",
    "suzuka.caches",
    "oauthsome.oauth_client",
    "oauthsome.oauth_server",
    "shared_login.login_consumer",
    "focalcrop",
    "shared_orgs_client",
    "corsheaders",
    "pitcrews_oauth2.accounts",
    "pitcrews_drafting",
    "django_filters",
    "ckeditor",
)


# Middleware

MIDDLEWARE = [
    "suzuka.pages.middleware.BetaHostMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "shared_login.login_consumer.middleware.SharedLoginRedirect",
    "drafting.EditModeMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "shared_orgs_client.middleware.OrgsMiddleware",
    "suzuka.conf.sites.CurrentRequestMiddleware",
    "suzuka.pages.editing.middleware.PageEditMiddleware",
    "suzuka.pages.middleware.AppendSlashMiddleware",
    "suzuka.conf.middleware.FeaturesMiddleware",
    "dj_pagination.middleware.PaginationMiddleware",
    "suzuka.stories.middleware.TheSeniorRedirectionMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "suzuka.pages.middleware.BetaRenderMiddleware",
    "suzuka.ab_testing.middleware.ExperimentMiddleware",
]


# Debugging

DEBUG = env("DJANGO_DEBUG", default=False, cast=bool)
TASTYPIE_FULL_DEBUG = DEBUG

if DEBUG:
    for optional in ("django_extensions", "debug_toolbar"):
        if importlib.util.find_spec(optional) is not None:
            INSTALLED_APPS += (optional,)

if "debug_toolbar" in INSTALLED_APPS:
    MIDDLEWARE = [
        "debug_toolbar.middleware.DebugToolbarMiddleware"
    ] + MIDDLEWARE
    DEBUG_TOOLBAR_PANELS = [
        "debug_toolbar.panels.sql.SQLPanel",
        "debug_toolbar.panels.cache.CachePanel",
        "suzuka.requests_panel.RequestsPanel",
        "debug_toolbar.panels.redirects.RedirectsPanel",
    ]
    INSTALLED_APPS += ("suzuka.requests_panel",)

if DEBUG:
    try:
        # Address when running locally on docker-compose.
        INTERNAL_IPS = [socket.gethostbyname("app-proxy")]
    except socket.gaierror:
        # Address when running on ECS.
        INTERNAL_IPS = [socket.gethostbyname(socket.gethostname())]


# Internationalisation / Localisation

USE_TZ = True
TIME_ZONE = "Australia/Sydney"

USE_I18N = False
USE_L10N = True
LANGUAGE_CODE = "en-au"


# Cache

MEMCACHE_LOCATION = env("DJANGO_MEMCACHE_LOCATION", default="local")
MEMCACHE_LOCATION_BACKENDS = {
    "local": "django.core.cache.backends.memcached.PyLibMCCache",
    "elasticache": "suzuka.cache.ZippyElastiCache",
}

MEMCACHE_HOSTS = env("DJANGO_MEMCACHE_HOSTS", default="", cast=Csv())

MEMCACHE_MIN_COMPRESS_LEN = 1024

if MEMCACHE_HOSTS:
    _CACHE_OPTIONS = {
        "behaviors": {
            "tcp_nodelay": True,
            "ketama": True,
            "remove_failed": 1,
            "retry_timeout": 1,
            "dead_timeout": 600,
        },
    }

    CACHES = {
        "default": {
            "BACKEND": MEMCACHE_LOCATION_BACKENDS[MEMCACHE_LOCATION],
            "LOCATION": MEMCACHE_HOSTS,
            "KEY_PREFIX": "/elc/default/",
            "BINARY": True,
            "OPTIONS": {} if DEBUG else _CACHE_OPTIONS,
        },
        "global_sessions": {
            "BACKEND": MEMCACHE_LOCATION_BACKENDS[MEMCACHE_LOCATION],
            "LOCATION": MEMCACHE_HOSTS,
            "KEY_PREFIX": "/elc/sessions/",
            "BINARY": True,
            "OPTIONS": {} if DEBUG else _CACHE_OPTIONS,
        },
        # Cache to be used for short-term response caching
        "response-cache": {
            "BACKEND": "calm_cache.backends.CalmCache",
            "LOCATION": MEMCACHE_LOCATION,
            "KEY_PREFIX": "response",
            "KEY_FUNCTION": "calm_cache.contrib.sha1_key_func",
            "OPTIONS": {
                "MINT_PERIOD": 5,
                "JITTER": 5,
            },
        },
        # Cache to be used for short-term response caching + grace
        "graceful-response-cache": {
            "BACKEND": "calm_cache.backends.CalmCache",
            "LOCATION": MEMCACHE_LOCATION,
            "KEY_PREFIX": "response",
            "KEY_FUNCTION": "calm_cache.contrib.sha1_key_func",
            "OPTIONS": {
                "MINT_PERIOD": 5,
                "GRACE_PERIOD": 3600 * 6,  # 6 hours
                "JITTER": 5,
            },
        },
        "elasticache": {
            "BACKEND": MEMCACHE_LOCATION_BACKENDS[MEMCACHE_LOCATION],
            "LOCATION": MEMCACHE_HOSTS,
            "BINARY": True,
            "OPTIONS": _CACHE_OPTIONS,
        },
        "local": {
            "BACKEND": MEMCACHE_LOCATION_BACKENDS[MEMCACHE_LOCATION],
            "LOCATION": MEMCACHE_HOSTS,
        },
    }

else:
    CACHES = {
        "default": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "default",
        },
        "response": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "response",
        },
        "response-cache": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "response",
        },
        "graceful-response-cache": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "response",
        },
        "global_sessions": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "global_sessions",
        },
    }

# Default settings for Calm Cache ResponseCache
CCRC_CACHE = "response-cache"

# Cache responses to requests with cookies
CCRC_CACHE_REQ_COOKIES = True

# Don't use cached responses for requests received via Varnish.
CCRC_NOCACHE_REQ_HEADERS = {
    "HTTP_X_VARNISH_CACHE": r"^\w+$",
    "HTTP_X_CACHE_DEVICE_TYPE": r"^\w+$",
}


# Database
pg_conn_max_age: Union[str, int] = os.environ.get("PGCONNMAXAGE", 0)

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ["PGDATABASE"],
        "USER": env("PGUSER"),
        "PASSWORD": env("PGPASSWORD"),
        "CONN_MAX_AGE": (
            None if pg_conn_max_age == "" else int(pg_conn_max_age)
        ),
    }
}

# Organization ID's

AAP_ORG_ID = "206"
FRM_NATIONAL_EDITORS_ORG_ID = "2"
THE_CANBERRA_TIMES_ORG_ID = "201"
SUPPORT_TEAM_ORG_ID = env("SUPPORT_TEAM_ORG_ID", "")
EXPLORE_TRAVEL_ORG_ID = env("EXPLORE_TRAVEL_ORGANIZATION_ID", "282")

# Security

SECRET_KEY = "=(d7%gc9&7rnpgi_&@aj(t!$pn+!29t-%nk1wt*3#qbf(05w1o"

ALLOWED_HOSTS = env("DJANGO_ALLOWED_HOSTS", default="*", cast=Csv())


CORS_URLS_REGEX = r"^/(?!((manage|admin)/)).*$"

CORS_ORIGIN_ALLOW_ALL = env(
    "DJANGO_CORS_ORIGIN_ALLOW_ALL", default="False", cast=bool
)

CORS_ORIGIN_REGEX_WHITELIST = (
    r"^https?://(?:(?:.+\.)?newsnow\.io|(?:.+\.)?legacy\.com)$",
)

CORS_ORIGIN_WHITELIST = env(
    "DJANGO_CORS_ORIGIN_WHITELIST", default="", cast=Csv()
)
CORS_ALLOW_CREDENTIALS = env(
    "DJANGO_CORS_ALLOW_CREDENTIALS", default=True, cast=bool
)
CORS_ALLOW_HEADERS = default_headers + ("cache-control",)

SESSION_ENGINE = "shared_login.shared_session"
SESSION_COOKIE_SECURE = True

MIDDLEWARE = ["suzuka.conf.sites.SecurityMiddleware"] + MIDDLEWARE

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTOCOL", "https")
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 60 * 60 * 12  # 1 hour sts
SECURE_REDIRECT_EXEMPT = ("^(?!manage/|admin/)(.*?)$",)
SECURE_FRAME_DENY = True

FILE_UPLOAD_MAX_MEMORY_SIZE = 30 * 1024 * 1024

# Disable SSL certificate verification.
SSL_VERIFY_CERTIFICATE = env(
    "DJANGO_SSL_VERIFY_CERTIFICATE", default=True, cast=bool
)
OAUTH_CLIENT_VERIFY_SSL = SLUMBER_VERIFY_SSL = SSL_VERIFY_CERTIFICATE

if not SSL_VERIFY_CERTIFICATE:
    # Prevent oauthlib InsecureTransportError
    os.environ["OAUTHLIB_INSECURE_TRANSPORT"] = "True"

# TODO: This is to facilitate multiple suzuka management domains (eg. for api calls) during domain migration.
PRIMARY_DOMAIN_ALIASES = env(
    "DJANGO_PRIMARY_DOMAIN_ALIASES",
    default="",
    cast=lambda v: [s.strip() for s in v.split(",")],
)

# Host names that should always be redirected to https, takes precedence over
# SECURE_REDIRECT_EXEMPT setting.
SUZUKA_SSL_REDIRECT_HOSTS: tuple[str, ...] = (os.environ["DJANGO_SUZUKA_URL"],)


# Logging

LOGGING = {
    "version": 1,
    "disable_existing_loggers": True,
    "formatters": {
        "simple": {
            "format": "[%(name)s] %(message)s",
        },
        "verbose": {
            "format": "%(asctime)s [%(name)s] %(levelname)s:%(lineno)d %(message)s",
        },
    },
    "filters": {
        "require_debug_false": {
            "()": "django.utils.log.RequireDebugFalse",
        }
    },
    "handlers": {
        "console": {
            "level": "DEBUG" if DEBUG else "INFO",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
    },
    # Base settings for all loggers that aren't explicitly configured or
    # don't have have 'propagate' : False
    #
    # By default, every logger emits messages with severity >= INFO
    # to `console` handler that is able to receive any level, including DEBUG
    "root": {
        "handlers": ["console"],
        "level": "DEBUG" if DEBUG else "INFO",
    },
    "loggers": {
        # Otherwise, Django logs all SQL queries
        "django.db.backends": {
            "level": "WARNING",
        },
        # Disable too verbose cors module: "INFO: Not a CORS request"
        "cors": {
            "level": "WARNING",
        },
        "requests": {
            "level": "WARNING",
        },
    },
}


# Error reporting

try:
    sentry_dsn = env("DJANGO_SENTRY_DSN")
    if sentry_dsn:
        # Ignore Ctrl-C in shell
        ignored_errors: list[Any] = [KeyboardInterrupt]

        # Ignore database errors outside production
        if ENVIRONMENT != "production":
            ignored_errors.append(OperationalError)

        sentry_sdk.init(
            dsn=sentry_dsn,
            environment=ENVIRONMENT,
            ignore_errors=ignored_errors,
            integrations=[
                DjangoIntegration(
                    cache_spans=False,
                    middleware_spans=False,
                    signals_spans=False,
                )
            ],
            release=env("RELEASE", default="unknown"),
            # If you wish to associate users to errors (assuming you are using
            # django.contrib.auth) you may enable sending PII data.
            send_default_pii=True,
        )
except UndefinedValueError:
    pass


# Templates

_LOADERS = [
    "suzuka.conf.loaders.ThemeLoader",
    "django.template.loaders.app_directories.Loader",
]

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(PROJECT_PATH, "theme/templates/legacy"),
            os.path.join(PROJECT_PATH, "templates"),
        ],
        "OPTIONS": {
            "debug": DEBUG,
            "loaders": _LOADERS
            if DEBUG
            else [("suzuka.conf.loaders.CachedLoader", _LOADERS)],  # type: ignore[list-item]
            "context_processors": [
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.debug",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.request",
                "django.contrib.messages.context_processors.messages",
                "pitcrews.layouts.context_processors.settings",
                "suzuka.conf.context_processors.conf",
            ],
            "builtins": [
                "pitcrews.layouts.templatetags.pitcrews_layouts",
                "suzuka.stories.templatetags.story_tags",
                "suzuka.absolute_uri.templatetags.absolute_uri_tags",
            ],
            "libraries": {
                "absolute_uri": "suzuka.absolute_uri.templatetags.absolute_uri_tags",
            },
        },
    },
]

THEME_CHOICES = (
    ("autumn", "Autumn"),
    ("base", "Base"),
    ("carbon", "Carbon"),
    ("hydrogen", "Hydrogen"),
    ("krypton", "Krypton"),
    ("legacy", "Legacy"),
    ("legolite", "Legolite"),
)

FULL_PAGE_THEMES = ("autumn",)


# Email

DEFAULT_FROM_EMAIL = "Suzuka <<EMAIL>>"
NEWSNOW_FROM_EMAIL = "NewsNow Publishing Platform <<EMAIL>>"
CAMPAIGN_FROM_EMAIL = "<EMAIL>"
CAMPAIGN_FROM_NAME = "Australian Community Media"

ADMINS = (("Pitcrews Team", "<EMAIL>"),)
MANAGERS = ADMINS
PITCREWS_APPNAME = PITCREWS_APP_NAME = "Suzuka"
PITCREWS_APP_LABEL = "Website Building"

SERVER_EMAIL = "<EMAIL>"
try:
    SERVER_EMAIL = "%s %s <%s>" % (
        PITCREWS_APPNAME,
        os.environ["DJANGO_ENVIRONMENT"],
        SERVER_EMAIL,
    )
except Exception:
    pass

EMAIL_SUBJECT_PREFIX = "[%s] " % PITCREWS_APPNAME
try:
    EMAIL_SUBJECT_PREFIX += "[%s:%s@%s] " % (
        PITCREWS_APPNAME,
        DEPLOY_TIMESTAMP,
        platform.node().split(".")[0],
    )
except Exception:
    pass

EMAIL_HOST = env("DJANGO_EMAIL_HOST", default=None)
EMAIL_PORT = env("DJANGO_EMAIL_PORT", default=25, cast=int)
EMAIL_HOST_USER = env("DJANGO_EMAIL_HOST_USER", default=None)
EMAIL_HOST_PASSWORD = env("DJANGO_EMAIL_HOST_PASSWORD", default=None)
EMAIL_USE_SSL = env("DJANGO_EMAIL_USE_SSL", default=False)

if EMAIL_USE_SSL:
    EMAIL_BACKEND = "django_smtp_ssl.SSLEmailBackend"
elif EMAIL_HOST is None:
    EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

ADVERTISE_WITH_US_TO = SELFSERVICE_ENQUIRIES_TO = [
    "<EMAIL>"
]

BUSINESS_PROFILES_ENQUIRIES_FROM = "<EMAIL>"

PRINT_BUNDLE_EMAIL_FROM = "<EMAIL>"
PRINT_BUNDLE_EMAIL_TO = ["<EMAIL>"]
PRINT_DIGITAL_BUNDLE_EMAIL_TO = [
    "<EMAIL>"
]

GROUP_SUBSCRIPTIONS_EMAIL_FROM = "<EMAIL>"
GROUP_SUBSCRIPTIONS_EMAIL_TO = ["<EMAIL>"]


# Static files

STATICFILES_DIRS = (
    os.path.join(PROJECT_PATH, "theme/static"),
    os.path.join(PROJECT_PATH, "static"),
)

STATICFILES_FINDERS = (
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
)

BRUNCH_HOTLOAD = env("BRUNCH_HOTLOAD", default=False, cast=bool)

STATIC_PATH = "/static_django/" if BRUNCH_HOTLOAD else "/static/"

STATIC_ROOT = os.path.join(os.environ["DJANGO_STATIC_DIR"], DEPLOY_TIMESTAMP)

STATIC_URL = (
    os.path.join(
        os.environ.get("DJANGO_STATIC_URL", STATIC_PATH), DEPLOY_TIMESTAMP
    )
    + "/"
)

if env("DJANGO_ENABLE_AKAMAI", default=False, cast=bool):
    AKAMAI_STATIC_URL = "/static/%s/" % DEPLOY_TIMESTAMP
    AKAMAI_MEDIA_URL = "https://serve.newsnow.io/"
    AKAMAI_TRANSFORM_URL = "/images/"


# Media

# Django now doesn't allowed media url to be within static url (when debug is true).
# As we don't seem to use media, lets map to actual media dir.

MEDIA_ROOT = os.path.join(os.environ["DJANGO_MEDIA_DIR"], DEPLOY_TIMESTAMP)

MEDIA_URL = (
    os.path.join(os.environ.get("DJANGO_MEDIA_URL", "media"), DEPLOY_TIMESTAMP)
    + "/"
)


# Admin / Dashboard

# `Site` for primary Suzuka domain. Management views will be affected
# if not set correctly.
SITE_ID = 5

NEWSNOW_DASHBOARD_URL = env("DJANGO_NEWSNOW_URL", default="https://newsnow.io")

PITCREWS_SERVICE_HOSTS = {
    "monza": os.environ["DJANGO_MONZA_URL"],
    "silverstone": os.environ["DJANGO_SILVERSTONE_URL"],
    "suzuka": "%smanage" % os.environ["DJANGO_SUZUKA_URL"],
    "valencia": os.environ["DJANGO_VALENCIA_URL"],
    "sepang": "%(host)s",
    "newsnow": NEWSNOW_DASHBOARD_URL,
}


# Django

ROOT_URLCONF = "suzuka.urls.sites"
APPEND_SLASH = False

# Django drafting will not work with the default session serializer. Once
# Drafting is updated to work with the Json serializer backend this can be
# removed.
SESSION_SERIALIZER = "django.contrib.sessions.serializers.PickleSerializer"


# Authentication (oAuth)

AUTHENTICATION_BACKENDS = (
    "newsnow_cognito.consumer.auth.CognitoAuthBackend",
    "shared_login.login_consumer.auth.SharedLoginBackend",
    "django.contrib.auth.backends.ModelBackend",
)

ACCOUNT_ACTIVATION_DAYS = 7

LOGIN_URL = reverse_lazy("cognito_consumer:login")
LOGIN_REDIRECT_URL = reverse_lazy("home")
LOGOUT_REDIRECT_URL = reverse_lazy("home")

OAUTH_CLIENT_SERVER_BACKEND = (
    "oauthsome.oauth_client.backends.database.SettingsBackend"
)


def logout_redirect_callback(request, **kwargs):
    from django.contrib import messages

    if getattr(request, "urlconf", "") == "suzuka.urls.backend":
        messages.add_message(
            request, messages.INFO, "You have been logged out."
        )
    return reverse_lazy("home")


SHARED_LOGIN_LOGOUT_REDIRECT_URL = logout_redirect_callback
SHARED_LOGIN_LOGIN_URLS = ("/manage/", "/manage/content/", "/accounts/login/")
SHARED_LOGIN_IGNORABLE_URLS = [
    r"/cognito/login/",
    r"/shared_login/webhook/[\w\d/]+",
]
SHARED_LOGIN_SERVER_NAME = "monza"
SHARED_LOGIN_SESSION_CACHE = "global_sessions"

MONZA_ACCOUNT_UPDATE_TOPICS = env(
    "DJANGO_MONZA_ACCOUNT_UPDATE_TOPICS",
    default="",
    cast=lambda v: [s.strip() for s in v.split(",")],
)


# Authentication (oAuth2)

PITCREWS_OAUTH2 = {
    "CACHE_PREFIX": "suzuka_oauth",
    "PROVIDERS": {
        "monza": {
            "access_token_url": "%so/token/" % os.environ["DJANGO_MONZA_URL"],
            "authorize_url": "%so/authorize/" % os.environ["DJANGO_MONZA_URL"],
            "tokeninfo_url": "%so/tokeninfo/" % os.environ["DJANGO_MONZA_URL"],
        },
    },
}

OAUTH2_PROVIDER = {
    "OAUTH2_VALIDATOR_CLASS": "pitcrews_oauth2.accounts.validators.RequestValidator",
}


# Host URLs

MONZA_HOST = os.environ["DJANGO_MONZA_URL"]
SILVERSTONE_HOST = os.environ["DJANGO_SILVERSTONE_URL"]
SUZUKA_HOST = os.environ["DJANGO_SUZUKA_URL"]
FUJI_HOST = os.environ["DJANGO_FUJI_URL"]
VALENCIA_HOST = os.environ["DJANGO_VALENCIA_URL"]
CDN_HOST = os.environ["DJANGO_CDN_URL"]
DIJON_HOST = os.environ["DJANGO_DIJON_URL"]
LONGBEACH_HOST = os.environ["DJANGO_LONGBEACH_URL"]
SOCHI_HOST = os.environ["DJANGO_SOCHI_URL"]
VALENCIA_TRANSFORM_HOST = os.environ["DJANGO_TRANSFORM_URL"]
TVGAPI_URL_PREFIX = os.environ["DJANGO_TVGAPI_URL"]
REACT_API_HOST = os.environ["REACT_API_HOST"]
ADONIS_API_HOST = os.environ["DJANGO_ADONIS_URL"]
PHOENIX_API_HOST = os.environ.get("DJANGO_PHOENIX_URL", "")
MONACO_HOST = os.environ.get("MONACO_HOST", "")
AGTRADER_HOST = os.environ["AGTRADER_HOST"]
SEPANG_HOST = os.environ.get(
    "DJANGO_SEPANG_URL", "https://sepang.dev.newsnow.io"
)

# Valencia Storage
VALENCIA_BUCKET = "suzuka-manage"
VALENCIA_CDN = CDN_HOST

# Sports Hub
SPORTSHUB_SQS = os.environ.get("SPORTSHUB_SQS", "")
SPORTSHUB_SSM_PARAM_NAME_DISABLE_PROCESSING = os.environ.get(
    "SPORTSHUB_SSM_PARAM_NAME_DISABLE_PROCESSING", ""
)

# Digital Print Edition (DPE)

# For DPE MVP, the DPE file server doesn't have https setup, so we will proxy https via sochi.
DPE_HOST_V1 = "%s/proxy/dpe" % SOCHI_HOST
DPE_HOST_V1_NON_PROXY = "specialpubs.fairfaxregional.com.au"

# TODO: Dynamic environments used in pipeline not configured with TF env vars,
# so need defaults.
DPE_HOST_V2 = os.environ.get("DPE_HOST", "")
DYNAMODB_DPE_TABLE = os.environ.get("DYNAMODB_DPE_TABLE", "")
DPE_CACHE_TIMEOUT = 60 * 30  # 30 mins

EXCLUSIVE_HOMES_API_URL = os.environ.get("EXCLUSIVE_HOMES_API_URL", "")

# E-Mags
DYNAMODB_EMAGS_TABLE = os.environ.get(
    "DYNAMODB_EMAGS_TABLE", "development-emags-issues"
)
SPECIAL_PUB_HOST = os.environ.get(
    "SPECIAL_PUB_HOST", "specialpubs.austcommunitymedia.com.au"
)
EMAGS_CACHE_TIMEOUT = 60 * 30  # 30 mins

# Time window for story guaranteed to not be in a CDN/memcache cache.
# ie. > STORY_CACHE_TIMEOUT (300 + max MINT'ing = 2130)
# > STORY_LIST_CACHE_TIMEOUT (450)
STORY_OUTSIDE_CACHE_WINDOW = 60 * 60  # 60 mins

SHARED_ORGS_API_PROVIDER = "%sorgs/api/v2" % MONZA_HOST

SILVERSTONE_API_HOST = "%sapi/v1/" % SILVERSTONE_HOST
SILVERSTONE_API2_HOST = "%sapi/v2/" % SILVERSTONE_HOST
DIJON_API_HOST = "%sapi/" % DIJON_HOST
LONGBEACH_API_HOST = "%sapi/" % LONGBEACH_HOST

PIANO_TRAFFIC_API_BASE_URL = os.environ.get(
    "PIANO_TRAFFIC_API_BASE_URL", "https://api.cxense.com"
)
PIANO_TRAFFIC_API_CX_USERNAME = os.environ.get(
    "PIANO_TRAFFIC_API_CX_USERNAME", "<EMAIL>"
)
PIANO_TRAFFIC_API_CX_API_KEY = os.environ.get(
    "PIANO_TRAFFIC_API_CX_API_KEY", "api&user&qMPL9+9MhDFVyOA//3Xbmw=="
)
PIANO_API_URL = os.environ["PIANO_API_URL"]
PIANO_ID_API_URL = os.environ["PIANO_ID_API_URL"]
PIANO_WEBHOOK_SQS_URL = os.environ.get("PIANO_WEBHOOK_SQS_URL", "")
PIANO_WEBHOOK_SNS_TOPIC = os.environ.get("PIANO_WEBHOOK_SNS_TOPIC", "")

if "sandbox" in PIANO_API_URL:
    PIANO_CDN_URL = "https://sandbox.tinypass.com"
else:
    PIANO_CDN_URL = "https://cdn-au.piano.io"
AD_PUBLISHER_PROVIDED_ID_SECRET = "CuElseUfBv0v9bnauIdg"

ADZUNA_JOBS_API = f"https://{SOCHI_HOST}/proxy/adzuna/"

MANAGEMENT_HOST = os.environ["DJANGO_MANAGEMENT_HOST"]

REACT_API_ENDPOINT = "/api/v1/render"
REACT_API_THEME_ENDPOINT = "/api/v1/theme"

# Legacy vars

# NOTE: Handle case where CDN_HOST is just domain or full url.
CDN_DOMAIN = (
    CDN_HOST.rstrip("/").split("://")[1]
    if CDN_HOST.startswith("http")
    else CDN_HOST
)

# Piano reports for Finance
FINANCE_SFTP_URL = "sftp://acmsubs:<EMAIL>"


# suzuka.conf

SITE_VISIBLE_OVERRIDE_URLS = [
    "/stories/",
    "/manage/",
    "/admin/",
    "/drafting/",
    "/api/",
    "/sns/story/",
    "/sns/story_published/",
    "/sns/farmbuy/",
    "/shared_login/",
    "/account/login/",
    "/account/logout/",
    "/health/",
    "/cognito/",
]

# Django debug toolbar for backend urls.
if DEBUG:
    SITE_VISIBLE_OVERRIDE_URLS += ["/__debug__/"]

CANONICAL_URL_SITES = {
    "www.brisbanetimes.com.au": {
        "name": "Brisbane Times",
        "domain": "www.brisbanetimes.com.au",
    },
    "news.domain.com.au": {
        "name": "Domain",
        "domain": "news.domain.com.au",  # Return news subdomain.
    },
    "www.essentialbaby.com.au": {
        "name": "Essential Baby",
        "domain": "www.essentialbaby.com.au",
    },
    "www.goodfood.com.au": {
        "name": "Good Food",
        "domain": "www.goodfood.com.au",
    },
    "www.smh.com.au": {
        "name": "The Sydney Morning Herald",
        "domain": "www.smh.com.au",
    },
    "news.smh.com.au": {  # Breaking news wire stories. Return main site.
        "name": "The Sydney Morning Herald",
        "domain": "www.smh.com.au",
    },
    "www.theage.com.au": {
        "name": "The Age",
        "domain": "www.theage.com.au",
    },
    "www.watoday.com.au": {
        "name": "WA Today",
        "domain": "www.watoday.com.au",
    },
}

# Live prefix whitelist used in finding official live site from org.
LIVE_DOMAIN_PREFIXES = ("www", "live")

# suzuka.pages

DEFAULT_PAGE_TEMPLATE = "one-column.html"

INFO_PAGE_TYPES = ["Feedback", "About Us", "Letters", "Contact"]


# Drafting

DRAFTING_CONTAINER_HANDLER = "suzuka.pages.editing.utils.edit_container_name"
DRAFTING_PERMS_HANDLER = "suzuka.pages.editing.utils.has_edit_perms"


# Django Rest Framework

DEFAULT_RENDERER_CLASSES = [
    "rest_framework.renderers.JSONRenderer",
]

if DEBUG:
    DEFAULT_RENDERER_CLASSES += [
        "rest_framework.renderers.BrowsableAPIRenderer",
    ]

REST_FRAMEWORK = {
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAdminUser",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "oauth2_provider.contrib.rest_framework.authentication.OAuth2Authentication",
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_RENDERER_CLASSES": DEFAULT_RENDERER_CLASSES,
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    "PAGE_SIZE": 250,
}


# GeoCode (Google Maps geolocation)
GEOCODE_GOOGLE_CLIENT_ID = "gme-fairfax"
GEOCODE_GOOGLE_SECRET_KEY = "46p1L30bqYcasBfBoyKx--mpRVc="

# Google Ad Manager
GOOGLE_AD_MANAGER_NETWORK_IDENTIFIER = "21666581298"


# Mailchimp
MAILCHIMP_API_KEY = "*************************************"
MAILCHIMP_AGS_API_KEY = "************************************"
MAILCHIMP_THE_SENIOR_API_KEY = "************************************"


NATIONAL_LIST_ID = "B236310A-220E-49B5-AABD-2F560416A48F"
NATIONAL_LISTS = {
    "The Informer": {
        "name": "The Informer",
        "description": "A daily newsletter providing a digest of national "
        "and world news to keep you up to date.",
        "icon": "images/newsletters/informer.png",
    },
    "Voice of Real Australia": {
        "name": "Voice of Real Australia",
        "description": "A daily newsletter bringing you updates from "
        "journalists in every state and territory.",
        "icon": "images/newsletters/vora.png",
    },
    "The Echidna": {
        "name": "The Echidna",
        "description": "The Echidna is a daily election newsletter with a difference. "
        "Featuring Australian writer Garry Linnell and cartoons of "
        "Fiona Katauskas.",
        "icon": "images/newsletters/echidna.png",
    },
    "Motoring": {
        "name": "Motoring",
        "description": "Stay up to date with the latest motoring news from across Australia.",
    },
    "Explore Travel": {
        "name": "Explore Travel",
        "description": "With destinations, deals and advice, plus travel writing to transport "
        "you around Australia and the globe, explore exciting holiday adventures and inspiring "
        "getaways with your Explore newsletter every Sunday.",
        "icon": "images/newsletters/explorer.png",
    },
    "Olympics 2024": {
        "name": "The Olympics Breakfast",
        "description": "Wake up to a tasty serving of overnight news and excitement from the "
        "2024 Olympic Games in Paris.",
    },
}

MAILCHIMP_API = f"https://{SOCHI_HOST}/proxy/Mailchimp/"
# The username subdomain is not required, but the data center is
MAILCHIMP_LIST_MANAGE = "https://us15.list-manage.com/"
MAILCHIMP_AGS_LIST_MANAGE = "https://us5.list-manage.com/"
MAILCHIMP_THE_SENIOR_LIST_MANAGE = "https://us5.list-manage.com/"
MAILCHIMP_ACCOUNT_ID = "3d952e1160da384a612b2b972"
MAILCHIMP_AGS_ACCOUNT_ID = "f821a3c0f9ebb195a03cb86d4"
MAILCHIMP_THE_SENIOR_ACCOUNT_ID = "0864364e7ccf8d854fbe6f386"

MARKETING_CLOUD_CLIENT_ID = "qdh29ojg8e34bsoihkidgyrn"
MARKETING_CLOUD_CLIENT_SECRET = "YfYQe1K5moaLJ97h4z8uB88K"
MARKETING_CLOUD_AUTHENTICATION_URL = (
    "https://mc1fhxg6tyvnb2517-c1r82gjkfy.auth.marketingcloudapis.com"
)
MARKETING_CLOUD_SOAP_URL = (
    "https://mc1fhxg6tyvnb2517-c1r82gjkfy.soap.marketingcloudapis.com"
)
MARKETING_CLOUD_REST_URL = (
    "https://mc1fhxg6tyvnb2517-c1r82gjkfy.rest.marketingcloudapis.com"
)
MARKETING_CLOUD_PAGES_URL = "https://cloud.mc.austcommunitymedia.com.au"
MARKETING_CLOUD_GLOBAL_DATA_EXTENSION_ID = (
    "CA647DC4-7AFC-4190-AE76-48935B90D579"
)
MARKETING_CLOUD_REMOTE_MAIL_GROUPS_DATA_EXTENSION_ID = (
    "D07DD92C-AAF8-43B9-8D26-4580AE5D0708"
)

MAIL_SQS_URL = os.getenv("MAILCHIMP_SQS")


# Retently

RETENTLY_API_KEY = "5b793453-f558-44de-8c59-83e5f6044348"


# Disqus

DISQUS_API_KEY = (
    "****************************************************************"
)


# Domain

DOMAIN_REAL_ESTATE_API = "https://api.domain.com.au/v1/listings/_search"
DOMAIN_REAL_ESTATE_AUTH_URL = "https://auth.domain.com.au/v1/connect/token/"
DOMAIN_REAL_ESTATE_AUTH_CLIENT = "6tevsyvv2htvkstu3dcvs55h"
DOMAIN_REAL_ESTATE_AUTH_SECRET = "x5wuSP5wZu"
DOMAIN_CONTENT_API_URL = (
    "https://s3-ap-southeast-2.amazonaws.com/"
    "domain-static/domainblog/straps-api/"
    "domain_acm_strap.json"
)

# Daily Motion

DAILY_MOTION_AUTH_CLIENT_ID = "9b516300cbcced47410f"
DAILY_MOTION_AUTH_CLIENT_SECRET = "e6fd40c80d4fa3f68ebabd6ef0a65b7a5afff9b3"
DAILY_MOTION_AUTH_URL = "https://api.dailymotion.com/oauth/token"
DAILY_MOTION_BASE_URL = "https://api.dailymotion.com"
DAILY_MOTION_AUTH_USERNAME = "<EMAIL>"
DAILY_MOTION_AUTH_PASSWORD = "N7#bee$iSfa!"


# Drive

DRIVE_URL = env(
    "DRIVE_URL",
    default="https://prod-boot.drive.com.au/wp-json/syndication/v1/get_syndication?destination=acm",
)
DRIVE_PASSWORD = "v4za XHIb g1UZ KieV wem9 ybOs"

# Explore
EXPLORE_URL = env(
    "EXPLORE_URL", default="https://www.exploretravel.com.au/wp-json/ai/posts/"
)
EXPLORE_API_KEY = env(
    "EXPLORE_API_KEY", default="C9tyNGaDxTZ4aMkfMJ5F8SbI0FlfNlVL"
)
EXPLORE_TRAVLR_API_KEY = env(
    "EXPLORE_TRAVLR_API_KEY", default="a92771cf-f682-4a2e-b98e-5c9038235e56"
)

# Realestate View
REALESTATE_VIEW_URL = env(
    "REALESTATE_VIEW_URL", default="https://uatr.view.com.au/api/nn-feed"
)
REALESTATE_VIEW_API_KEY = env(
    "REALESTATE_VIEW_API_KEY", default="9594f5d7-be39-482d-aaa6-646dfffd8c15"
)
REALESTATE_VIEW_ORGANIZATION_ID: str = env(
    "REALESTATE_VIEW_ORGANIZATION_ID", default="275"
)
REALESTATE_VIEW_SITE_ID = env("REALESTATE_VIEW_SITE_ID", default="542")

# RFS View
NSW_RFS_VIEW_URL = env(
    "NSW_RFS_VIEW_URL",
    default="https://www.rfs.nsw.gov.au/feeds/majorIncidents.json",
)

# Live Traffic
LIVE_TRAFFIC_API_KEY = os.getenv("LIVE_TRAFFIC_API_KEY", "")

# Allhomes
ALLHOMES_URL = (
    "s3://ffxblue-integrations-domain-cu/domain_canberratimes_strap.json"
)
ALLHOMES_FEATURED_URL = (
    "s3://ffxblue-integrations-domain-cu/domain_canberratimes_featured.json"
)
# Farmbuy
# Default to dev bucket if not in specified envs since there are no buckets for dynamic envs
farmbuy_env = (
    ENVIRONMENT
    if ENVIRONMENT in ("uat", "staging", "production")
    else "development"
)
FARMBUY_FEATURED_PROPERTIES_URL = (
    f"s3://farmbuy-featured-properties-{farmbuy_env}/featured_properties.json"
)

# Slumber Party

SLUMBER_PARTY_APIS = {
    "longbeach": {
        "client": "suzuka.business.longbeach_client.API",
        "url_setting": "LONGBEACH_API_HOST",
    },
    "silverstone": {
        "client": "silverstone_client.client.API",
        "url_setting": "SILVERSTONE_API_HOST",
    },
    "silverstone_v2": {
        "client": "suzuka.stories.silverstone_v2.API",
        "url_setting": "SILVERSTONE_API2_HOST",
    },
    "dijon": {
        "client": "dijon.client.API",
        "url_setting": "DIJON_API_HOST",
    },
    "adonis": {
        "client": "suzuka.stories.adonis.API",
        "url_setting": "ADONIS_API_HOST",
    },
}

# Airship

AIRSHIP_PUSH_NOTIFICATION_URL = "https://go.urbanairship.com/api/push/"
AIRSHIP_BATCH_SEND_LIMIT = 800

# Ads

DOUBLE_CLICK_CTYPE_MAP = {
    None: "index",
    "gallery": "ffxphotogallery",
    "story": "article",
}

DOUBLE_CLICK_FIXED_PARAMS = {
    "728x90,468x60": {"nav": {"pos": 1, "tile": 10}},
}

DOUBLE_CLICK_NETWORK_ID = "N6411"

#  This setting is used in ads app to force Last-Modified on
#  certain semi-static pages.
#  It's impossible to use actual files' mtimes properly in Pitcrews
#  environment, because deploy tool preserves timestamps from the deploying
#  person's temporary mercurial repository, reflecting last checkout time
#  which is not only different among developers, but could be set in the
#  future if clocks aren't synchronised.
#  Setting Last-Modified to a static value is not possible
#  as well, because the files potentially could be changed with
#  every deploy.
try:
    AD_OVERRIDE_TIMESTAMP = int(DEPLOY_TIMESTAMP)
except Exception:
    pass

# Chartbeat

CHARTBEAT_UID = 66019
# for toppages, summize, recent api
CHARTBEAT_LIVE_API_KEY = "5cbe3161ab75c7268a20c099fc6d9a2d"
# for all api
CHARTBEAT_ALL_API_KEY = "1c1744dba92320c6841fd421445e7f95"
CHARTBEAT_ENDPOINT = "https://api.chartbeat.com/live/toppages/v3/"
CHARTBEAT_SUBMIT_QUERY_ENDPOINT = (
    "https://api.chartbeat.com/query/v2/submit/page/"
)
CHARTBEAT_FETCH_QUERY_ENDPOINT = "https://api.chartbeat.com/query/v2/fetch/"
CHARTBEAT_TOP_STORIES_CACHE_TIMEOUT = 60

# Sensis
SENSIS_API_USERNAME = "ffm_tc_prod"
SENSIS_API_PASSWORD = "qsU9Bzv6"

# Brightcove
DEFAULT_BRIGHTCOVE_ACCOUNT_ID = "*************"
DEFAULT_BRIGHTCOVE_PLAYER_ID = "cdO538E0l"
# Duplicate Brightcove player without the advertisements
# The advertisement is configured in the server(brightcove) side.
# Replace the default player with ad-free for Premium Subscription
ADFREE_BRIGHTCOVE_PLAYER_ID = "acJAzgBUQ"

# Dailymotion
DEFAULT_DAILYMOTION_PLAYER_ID = "x7206"
DEFAULT_DAILYMOTION_PLAYER_ID_FOR_NO_AUTOPLAY = "xi6v4"
DEFAULT_DAILYMOTION_PLAYER_ID_FOR_VIDEO_SHORTS = "xjtu3"
DEFAULT_DAILYMOTION_PLAYER_ID_FOR_APP = "x7mrq"
DEFAULT_DAILYMOTION_PLAYER_ID_FOR_LOOPER_VIDEOS = "x13dp6"
DEFAULT_DAILYMOTION_PLAYER_ID_FOR_EXPLORE_TRAVEL_ARTICLES = "x1a8q0"
DAILYMOTION_SYNDICATION_KEY = "790147"

# JotForm
JOTFORM_API_KEY = os.environ.get("JOTFORM_API_KEY", "")

# Crispy Forms
CRISPY_TEMPLATE_PACK = "bootstrap4"

# Reversion
ADD_REVERSION_ADMIN = True


# Application status

PITCREWS_HEALTH_CHECKS = ["databases", "caches", "content"]


# Newsnow Cognito Common/Consumer settings

COGNITO_AUTH_URL = env("DJANGO_COGNITO_AUTH_URL", default="")
COGNITO_APP_CLIENT_ID = env("DJANGO_COGNITO_APP_CLIENT_ID", default="")
COGNITO_LOGIN_REDIRECT_URL = env(
    "DJANGO_COGNITO_LOGIN_REDIRECT_URL",
    default="{}/cognito/authorize/".format(SUZUKA_HOST.rstrip("/")),
)
COGNITO_LOGIN_SERVER_CLIENT = env(
    "DJANGO_COGNITO_LOGIN_SERVER_CLIENT", default="suzuka"
)

SESSION_COOKIE_DOMAIN = "." + urlparse(SUZUKA_HOST).netloc

# CKEditor

CKEDITOR_CONFIGS = {
    "default": {
        "toolbar": "Custom",
        "toolbar_Custom": [
            ["Format", "Bold", "Italic", "Underline"],
            [
                "NumberedList",
                "BulletedList",
                "-",
                "Outdent",
                "Indent",
                "-",
                "JustifyLeft",
                "JustifyCenter",
                "JustifyRight",
                "JustifyBlock",
            ],
            ["Link", "Unlink", "Image", "Table"],
            ["Paste", "PasteText", "PasteFromWord", "CopyFormatting"],
            ["Undo", "RemoveFormat", "Source", "Maximize"],
        ],
        "height": 300,
        "width": "100%",
        "baseFloatZIndex": 100000,  # High enough to be above Preview button in content edit view.
        "removePlugins": "stylesheetparser",  # Need this to allow setting allowedContent
        # https://ckeditor.com/docs/ckeditor4/latest/api/CKEDITOR_config.html#cfg-allowedContent
        # "true – will disable the filter (data will not be filtered, all features will be activated)"
        "allowedContent": True,
    },
}


# Viafoura comments
VIAFOURA_CLIENT_ID = os.environ.get("VIAFOURA_CLIENT_ID", "")
VIAFOURA_SECRET = os.environ.get("VIAFOURA_SECRET", "")


# Norkon Liveblog
NORKON_API_URL = os.environ.get("NORKON_API_URL", "")
NORKON_BASE_URL = os.environ.get("NORKON_BASE_URL", "")
NORKON_WEBSOCKET_URL = os.environ.get("NORKON_WEBSOCKET_URL", "")
NORKON_SCRIPT_URL = os.environ.get(
    "NORKON_SCRIPT_URL",
    "",
)
NORKON_TENANT_KEY = os.environ.get("NORKON_TENANT_KEY", "")
NORKON_PUGPIG_APP_ENV = "PUGPIG"


# Django Solo
SOLO_CACHE = "default"
SOLO_CACHE_PREFIX = "solo"
SOLO_CACHE_TIMEOUT = 60 * 5

# Page config
MAX_PAGE_DEPTH = 5

# Local settings

try:
    from .local_settings import *  # noqa: F403
except ImportError:
    pass
