# Generated by Django 3.1.14 on 2025-09-05 04:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('conf', '0205_settings_classifiedsfeature_use_national_index'),
    ]

    operations = [
        migrations.AddField(
            model_name='settings',
            name='mobileappfeature_branch_io_public_key',
            field=models.CharField(blank=True, default='', help_text='Find Your Branch Key: In the Branch Dashboard, go to Account Settings > Profile. Your Branch Key will be listed there', max_length=100, verbose_name='Branch.io Public Key'),
        ),
    ]
