import os

import reversion
from django.conf import settings
from django.contrib.auth.models import Group, User
from django.contrib.sites.models import Site
from django.core.validators import RegexValidator
from django.db import models
from django.db.models.base import ModelBase
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from geocode.backends import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get_backend
from geocode.models import GeocodeBase
from shared_orgs_client.utils import OrgsManager, OrgsMixin
from solo.models import SingletonModel
from timezone_field.fields import TimeZoneField

from suzuka.conf.utils import (
    DEFAULT_GALLERY_LAYOUT,
    DEFAULT_GALLERY_STYLE,
    GALLERY_LAYOUT_CHOICES,
    GALLERY_STYLES_CHOICES,
    ModelDiffMixin,
    chaperone_site_field_change,
)
from suzuka.conf.validators import validate_sports_sponsor_data
from suzuka.targetlists.utils import (
    cache_target_lists,
    list_api,
    site_resource,
)

from .fields import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>A<PERSON>yField


def make_field_name(name, suffix):
    return f"{name.lower()}_{suffix}"


@receiver(post_save, sender=User)
def set_default_group(sender, **kwargs):
    """
    Assign new users to the default group. Since we can't consistently
    identify which group this is without hard-coding an ID, we can
    presume it's the first group - migrations were also added with
    this code that removed all groups and created the initial group
    with default permissions. This is a temporary work-around until
    distributed permissions management has been restored.
    """
    if kwargs["created"]:
        user = kwargs["instance"]
        groups = Group.objects.all().order_by("id")
        if groups:
            user.groups.add(groups[0])


class CustomOrgsManager(OrgsManager):
    def allowed_for_user(self, user):
        if user.has_perm("conf.access_all_sites"):
            return self.all()
        return self.for_user()


@reversion.register()
class Settings(OrgsMixin, Site, GeocodeBase, ModelDiffMixin):  # type: ignore[django-manager-missing]
    """
    Implements editable settings on to site objects.
    """

    STORY_LAYOUT_MULTIPLE = "MULTIPLE"
    STORY_LAYOUT_SINGLE = "SINGLE"
    STORY_LAYOUT_EXPLORE = "EXPLORE"
    STORY_LAYOUT_ECHIDNA = "ECHIDNA"
    STORY_LAYOUT_MOP = "MOP"

    STORY_LAYOUT_CHOICES = (
        (STORY_LAYOUT_MULTIPLE, _("Multiple stories")),
        (STORY_LAYOUT_SINGLE, _("Single story")),
        (STORY_LAYOUT_EXPLORE, _("Explore story")),
        (STORY_LAYOUT_ECHIDNA, _("The Echidna story")),
        (STORY_LAYOUT_MOP, _("MOP story")),
    )

    THEME_VARIANT_DEFAULT = "DEFAULT"
    THEME_VARIANT_AGS = "AGS"
    THEME_VARIANT_EXPLORE = "EXPLORE"
    THEME_VARIANT_ECHIDNA = "ECHIDNA"
    THEME_VARIANT_MOP = "MOP"

    THEME_VARIANT_CHOICES = (
        (THEME_VARIANT_DEFAULT, _("Default")),
        (THEME_VARIANT_AGS, _("Agricultural")),
        (THEME_VARIANT_EXPLORE, _("Explore Travel")),
        (THEME_VARIANT_ECHIDNA, _("The Echidna")),
        (THEME_VARIANT_MOP, _("MOP")),
    )

    public_settings = (
        "domain",
        "facebook_url",
        "latitude",
        "longitude",
        "location",
        "name",
        "publication",
        "twitter_username",
        "theme_dir",
        "google_search_id",
        "timezone",
        "ga_id_1",
    )

    static_dir = models.CharField(max_length=100, blank=True)

    # Editable settings.

    publication = models.CharField(_("Short Name"), max_length=100, blank=True)
    www_site_id = models.PositiveIntegerField(
        _("WWW site id"),
        blank=True,
        null=True,
        help_text="Set a site id for the beta site to load data for the classifieds page, "
        "please ignore this field if the site is not the beta version.",
    )

    timezone = TimeZoneField(
        blank=True,
        null=True,
        help_text="Display dates and times in this"
        " timezone. '%s' is used if not selected." % settings.TIME_ZONE,
    )
    visible = models.BooleanField("Visible", default=False)
    web_crawlers = models.BooleanField("Web Crawlers", default=True)

    # Google Analytics
    ga_id_1 = models.CharField(
        _("Google Analytics ID (1)"), max_length=100, blank=True
    )
    ga_domain_1 = models.CharField(
        _("Google Analytics Domain (1)"), max_length=100, blank=True
    )
    ga_id_2 = models.CharField(
        _("Google Analytics ID (2)"), max_length=100, blank=True
    )
    ga_domain_2 = models.CharField(
        _("Google Analytics Domain (2)"), max_length=100, blank=True
    )
    ga_id_3 = models.CharField(
        _("Google Analytics ID (3)"), max_length=100, blank=True
    )
    ga_domain_3 = models.CharField(
        _("Google Analytics Domain (3)"), max_length=100, blank=True
    )
    # Google Search
    google_search_id = models.CharField(
        _("Google Search ID"), max_length=100, blank=True
    )
    google_vertex_config_id = models.CharField(
        _("Google Vertex Config ID"), max_length=100, blank=True
    )

    # Double-click
    double_click_site = models.CharField(
        _("DoubleClick site"), max_length=100, default="onl.fairfaxregional"
    )
    double_click_zone = models.CharField(
        _("DoubleClick zone"), max_length=100, blank=True
    )
    double_click_cat = models.CharField(
        _("DoubleClick cat"), max_length=100, blank=True
    )
    double_click_region = models.CharField(
        _("DoubleClick region"), max_length=100, blank=True
    )
    double_click_state = models.CharField(
        _("DoubleClick state"), max_length=100, blank=True
    )

    # Navigation
    index_ab_test = models.BooleanField(
        _("Index Page A/B Test"), default=False
    )
    nav_top_down_ad_cat_targeting = models.BooleanField(
        _("Top Down Ad Cat Targeting"), default=False
    )
    nav_ct_navbar = models.BooleanField(_("CT Navbar"), default=False)
    nav_left_content = models.CharField(
        _("Nav Left Content"),
        blank=True,
        max_length=20,
        choices=(
            ("business", "Business"),
            ("puzzles", "Puzzles"),
            ("puzzles_paywall", "Puzzles - Paywall"),
            ("puzzles_paywall_v2", "Puzzles - Paywall ver 2"),
            ("puzzles_crosswords", "Puzzles - Crosswords"),
        ),
    )

    # Social Media
    facebook_url = models.CharField(
        _("Facebook Page URL"), max_length=100, blank=True
    )
    facebook_page_id = models.CharField(
        _("Facebook Page Id"), max_length=100, blank=True
    )
    twitter_username = models.CharField(
        _("Twitter Username"), max_length=100, blank=True
    )
    instagram_username = models.CharField(
        _("Instagram Username"), max_length=100, blank=True
    )
    youtube_url = models.CharField(
        _("Youtube Channel URL"), max_length=100, blank=True
    )

    # Misc
    story_layout = models.CharField(
        _("Default story layout"),
        max_length=20,
        choices=STORY_LAYOUT_CHOICES,
        default=STORY_LAYOUT_MULTIPLE,
    )
    disable_story_page_rhs_storylist = models.BooleanField(
        _("Disable RHS Storylist"),
        default=False,
        help_text=("Disable RHS storylist from story pages."),
    )
    disable_story_page = models.BooleanField(
        default=False,
        help_text="Diables all but the story-lean view for story pages.",
    )
    disable_signpost_for_story_free_tag = models.BooleanField(
        _("Disable adding signpost for stories with 'free' tag"),
        default=False,
        help_text=(
            "Disables rendering an extra 'signpost-free' tag when a story had a 'free' tag."
            " Note: If story already has 'signpost-free' tag, this doesn't impact that."
        ),
    )
    enable_story_localad = models.BooleanField(
        default=False,
        help_text="Enables the local ads widget inside an article body.",
    )

    display_rev_notification = models.BooleanField(
        _("Show View banner"),
        default=False,
        help_text="Displays the View notification banner",
    )

    view_url = models.CharField(
        _("View URL"),
        max_length=200,
        blank=True,
        help_text="URL used by the View notification banner",
    )

    display_ags_banner = models.BooleanField(
        _("Show AGS banner"),
        default=False,
        help_text="Displays the AGS Vodcast amplification banner on article pages.",
    )

    optimizely_id = models.CharField(max_length=10, blank=True)

    # Centre location for business search
    location = models.TextField(null=True, blank=True)
    postcode = models.CharField(
        blank=True,
        max_length=4,
        help_text="Optional: Only needed for weather widget at certain locations.",
    )
    radius = models.IntegerField(
        default=20,
        editable=False,
        help_text="Distance (km) from location, for the region covered by this site (km)",
    )

    business_directory_enabled = models.BooleanField(
        default=False, editable=False
    )

    theme_dir = models.CharField(
        _("Theme"),
        max_length=100,
        blank=True,
        default="autumn",
        choices=settings.THEME_CHOICES,  # type: ignore[misc]
    )

    theme_variant = models.CharField(
        _("Theme Variant"),
        max_length=30,
        default=THEME_VARIANT_DEFAULT,
        choices=THEME_VARIANT_CHOICES,
    )

    use_suzuka_ui = models.BooleanField(
        default=False, help_text="Enable Suzuka-UI page rendering."
    )

    default_comment_feature = models.CharField(
        default="disqus",
        blank=True,
        max_length=20,
        help_text="Not supported for legacy theme",
        choices=(
            ("disqus", "Disqus"),
            ("facebook", "Facebook"),
            ("viafoura", "Viafoura"),
        ),
    )

    chartbeat_domain = models.CharField(
        blank=True,
        help_text=_(
            "Optional override if Chartbeat domain is different from the "
            "site's domain",
        ),
        max_length=253,
    )

    domain_verify_code_google = models.CharField(
        _("Google verify code"), blank=True, max_length=50
    )

    domain_verify_code_facebook = models.CharField(
        _("Facebook verify code"), blank=True, max_length=50
    )

    domain_verify_code_bing = models.CharField(
        _("Bing verify code"), blank=True, max_length=50
    )

    index_exchange_id = models.CharField(
        default="186432-278594325649166", blank=True, max_length=50
    )

    use_clean_story_url = models.BooleanField(
        default=False,
        help_text=_(
            "Enabling it will exclude cs param in the story URL i.e. ?cs=<storylist_id> will not be added to Story URL"
        ),
    )

    class Meta:
        verbose_name_plural = _("Settings")
        ordering = ("name",)
        permissions = (("access_all_sites", "Can access all sites"),)

    orgs_manager = CustomOrgsManager()

    def save(self, *args, **kwargs):
        GeoFeature.update_geo(self)

        if not self.static_dir:
            self.static_dir = slugify(str(self.name))

        chaperone_site_field_change(self)

        created = not self.id
        super().save(*args, **kwargs)
        if created and self.publication:
            api = list_api()
            new_list = api.list.post(
                {
                    "name": self.publication,
                    "organization": self.organization,
                }
            )
            api.member.post(
                {
                    "resource": site_resource(self.id),
                    "list": new_list.resource_uri,
                }
            )
            cache_target_lists(self.id)
        Features.objects.get_or_create(settings=self)

    def get_lookup_address(self):
        return self.businessfeature_location


class GoogleNewsRundownExcludedTags(SingletonModel):
    excluded_tags = models.CharField(
        max_length=500,
        blank=True,
    )

    def __str__(self):
        return "Google News Rundown Excluded Tags"

    class Meta:
        verbose_name = "Google News Rundown Excluded Tags"


class Features(models.Model):
    """
    One to one with Site/Settings which holds each of the boolean
    fields for enabling each of the features per site. Boolean
    fields are dynamically added to this class for each ``Feature``
    subclass, which occurs inside the ``FeatureBase`` metaclass.
    """

    class Meta:
        verbose_name_plural = "Features"

    settings = models.OneToOneField(
        Settings, on_delete=models.deletion.CASCADE
    )
    registry = {}

    def __str__(self):
        return str(self.settings)

    def __getattr__(self, name):
        """
        Dynamic lookup for each of the ``Feature`` class attributes, eg:
        feature.excluded_zone_items, feature.excluded_page_layouts, etc.
        We also support proxying to methods on each of these features.
        """
        if name.startswith("excluded_"):
            return self._excluded_items(name.replace("excluded_", "", 1))
        else:
            proxied_method = self._proxied_method(name)
            if proxied_method:
                return proxied_method
        raise AttributeError(name)

    def _features(self, enabled=True):
        """
        Returns the registry features that are either enabled or
        disabled for this instance's site.
        """
        for enabled_fieldname, feature_class in list(self.registry.items()):
            if getattr(self, enabled_fieldname) == enabled:
                yield feature_class

    def _excluded_items(self, name):
        """
        Given one of the class attributes of the ``Feature`` model
        that stores sequences of items, iterates through each of the
        ``Feature`` subclasses (stored in ``Features.registry``) and
        returns the combined list of items from all features that
        aren't enabled for the site.
        """
        items = []
        for feature_class in self._features(enabled=False):
            items.extend([s.lower() for s in getattr(feature_class, name)])

        # An item may exist in different Features. Remove items in enabled
        # features from the excluded items.
        for feature_class in self._features(enabled=True):
            for included_item in [
                s.lower() for s in getattr(feature_class, name)
            ]:
                if included_item in items:
                    items.remove(included_item)
        return items

    def _proxied_method(self, name):
        """
        Called from ``__getattr__`` - supports method calls on
        ``Features`` instances, that proxy to methods defined
        on ``Feature`` classes.

        NOTE: this isn't actually used yet.
        """
        for feature_class in self._features(enabled=True):
            if hasattr(feature_class, name):
                feature_instance = feature_class.from_settings(self.settings)
                return getattr(feature_instance, name)


class FeatureBase(ModelBase):
    """
    Metaclass for the ``Feature`` model. The ``Feature`` model
    doesn't actually have a db table (it's abstract) and exists
    entirely to implement the features API.

    A feature is defined by subclassing the ``Feature`` model. A
    feature contains the model fields for any database backed
    settings that the feature provides. When the ``Feature`` model
    is subclassed, a couple of things occur in ``__new__`` below.
    For example suppose we have a feature defined as::

        class BusinessFeature(Feature):
            some_setting = models.BooleanField()

    ... then the following occurs:

    1) ``BusinessFeature`` gets forced to be an abstract model,
       so it won't actually have a database table.
    2) ``Features`` model (note plural) above is given a boolean
       field ``businessfeature_enabled`` for enabling the feature.
    3) All of the model fields on ``BusinessFeature`` get copied
       onto the ``Settings`` model, prefixed with the feature name.
       These are then only displayed in the model form for ``Settings``
       if the feature is enabled for the related site. Given the
       ``some_setting`` field above, it would end up named as
       ``Settings.businessfeature_some_setting``.

    These features should be stored in a module called ``features.py``
    in one of the project's ``INSTALLED_APPS``. These are then
    imported automatically below.
    """

    def __new__(cls, name, bases, attrs):
        is_feature = name not in ["Feature", "GeoFeature"]
        if is_feature:

            class Meta:
                abstract = True

            attrs["Meta"] = Meta
            # We assign the list of settings field names copied over
            # so that we can treat them like the other feature
            # attributes and see whhich ones should be excluded for
            # a given site in the Settings model form.
            attrs["settings_names"] = []
            enabled_fieldname = make_field_name(name, "enabled")
            default = attrs.get("default_enabled", False)
            enabled = models.BooleanField(attrs["title"], default=default)
            enabled.contribute_to_class(Features, enabled_fieldname)
            for klass in bases:
                try:
                    attrs.update(klass.get_attrs())
                except AttributeError:
                    pass
            for k, v in list(attrs.items()):
                if isinstance(v, models.Field):
                    setattr(v, "feature_title", attrs["title"])
                    settings_fieldname = make_field_name(name, k)
                    attrs["settings_names"].append(settings_fieldname)
                    v.contribute_to_class(Settings, settings_fieldname)
                    del attrs[k]
        feature = super().__new__(cls, name, bases, attrs)
        if is_feature:
            Features.registry[enabled_fieldname] = feature
        return feature


class Feature(models.Model, metaclass=FeatureBase):
    """
    Base ``Feature`` model for features to subclass. See its
    metaclass ``FeatureBase`` for implementation details. Some
    other class members are implemented as part of the features
    API::

    ``Feature.title``:
        Name of the feature - used as the ``feature_enabled``field
        label on the ``Features`` model, and also for the formset
        heading in the modelform for the ``Settings`` model, to group
        the feature's settings fields.

    ``Feature.default_enabled``:
        Boolean defining whether the feature is enabled by default
        for new sites.

    ``Feature.urlpatterns``:
        A sequence of urlpattern *names* that are only accessible
        when the feature is enabled for the site.

    ``Feature.zone_items``:
        A sequence of zone item models (in the format
        ``app_label.model_name``) that are only available in live-edit
        mode, when the feature is enabled for the site.

    ``Feature.layout_templates``:
        A sequence of template path/names for page layouts that are
        only available in live-edit mode, when the feature is enabled
        for the site.

    """

    title = ""
    default_enabled = False
    urlpatterns: tuple[str, ...] = ()
    zone_items: tuple[str, ...] = ()
    layout_templates: tuple[str, ...] = ()

    @classmethod
    def from_settings(cls, settings):
        """
        Allows any ``Feature`` class to be instantiated from a settings
        instance. The feature's field values are populated from the
        settings instance. This is to support methods on ``Feature``
        classes that are then callable from instances of the ``Features``
        model.

        Possible TODO (from CB):

        - Have a proxy descriptor to the class for the fields
        - When the Feature is instanciated you give it a settings
          instance
        - Then the descriptor looks up the relevant field in the
          settings instance
        - Added advantage of this is that the feature class would
          always reflect the values in settings and the method
          could write back

        """
        feature = cls()
        prefix = cls.__name__.lower() + "_"
        for field in settings._meta.fields:
            if field.name.startswith(prefix):
                name = field.name.replace(prefix, "", 1)
                setattr(feature, name, getattr(settings, field.name))
        return feature


class GeoFeature(Feature):
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)

    last_location = models.TextField(
        "Last Location", null=True, editable=False, default=""
    )
    location = models.TextField("Location")
    radius = models.IntegerField(
        "Radius (km)",
        default=20,
        help_text="Distance (km) from location, for the region covered by this feature (km)",
    )

    @staticmethod
    def update_geo(settings):
        if hasattr(settings, "features"):
            for feature in settings.features._features(enabled=True):
                if not isgeofeature(feature):
                    continue

                prefix = feature._meta.model.__name__.lower() + "_"

                last_location_field = prefix + "last_location"
                last_location = getattr(settings, last_location_field)
                location_field = prefix + "location"
                location = getattr(settings, location_field)

                if last_location != location:
                    setattr(settings, last_location_field, location)
                    latitude_field = prefix + "latitude"
                    longitude_field = prefix + "longitude"
                    geocoder = get_backend()
                    result = geocoder.geocode(location)
                    if isinstance(result, ErrorResult):
                        setattr(settings, latitude_field, None)
                        setattr(settings, longitude_field, None)
                    else:
                        setattr(settings, latitude_field, result.latitude)
                        setattr(settings, longitude_field, result.longitude)

    @classmethod
    def get_attrs(cls):
        attrs = {}
        attrs["latitude"] = models.FloatField(null=True, blank=True)
        attrs["longitude"] = models.FloatField(null=True, blank=True)
        attrs["last_location"] = models.TextField(
            "Last Location", null=True, editable=False, default=""
        )
        attrs["location"] = models.TextField("Location")
        attrs["radius"] = models.IntegerField(
            "Radius (km)",
            default=20,
            help_text="Distance (km) from location, for the region covered by this feature (km)",
        )
        return attrs


def isgeofeature(feature):
    return GeoFeature in feature._meta.get_parent_list()


def issubfeature(feature):
    return len(feature._meta.get_parent_list()) > 1


class AdobeTagManagerFeature(Feature):
    """
    Ability to turn Titan ads on and off.

    TODO Rename this feature as its purpose has changed
    """

    title = "Titan ads"
    default_enabled = True


class AduznaJobBoxFeature(Feature):
    """
    Feature to enable/disable the adzuna job box widget from pages on a site
    """

    title = "Adzuna Job Box"
    job_box_publication = models.CharField(max_length=200, blank=True)
    search_location = models.CharField(blank=True, max_length=100)


class HindsightFeature(Feature):
    """
    Feature to enable/disable the Hindsight widget from article page on a site
    """

    title = "Hindsight"
    default_enabled = False


class TooltipsFeature(Feature):
    """
    Feature to enable/disable the tooltips widget
    """

    title = "Tooltips"
    default_enabled = True


# Features API supports defining features in an app's ``features.py``
# model - make sure we load them.
for app in settings.INSTALLED_APPS:
    try:
        __import__("%s.features" % app)
    except ImportError:
        pass


class PolarFeature(Feature):
    """
    Feature to enable/disable polar scripts
    """

    title = "Polar"
    default_enabled = False


class PlistaOutstreamFeature(Feature):
    """
    Feature to enable/disable plista outstream feature
    """

    title = "Plista Outstream"
    default_enabled = False
    public_key = models.CharField(blank=True, max_length=100)


class TeadsFeature(Feature):
    """
    Feature to enable/disable teads feature
    """

    title = "Teads"
    default_enabled = False


class PianoFeature(Feature):
    """
    Feature to enable/disable Piano feature
    """

    PIANO_CTA_DEFAULT = "DEFAULT"
    PIANO_CTA_SUMMER_SALE = "SUMMER_SALE"
    PIANO_CTA_WINTER_SPORTS = "WINTER_SPORTS"
    PIANO_CTA_FOOD_MONTH = "FOOD_MONTH"
    PIANO_CTA_MOTHERS_DAY = "MOTHERS_DAY"
    PIANO_CTA_EOFY_2021 = "EOFY_2021"
    PIANO_CTA_FREE_FUEL = "FREE_FUEL"
    PIANO_CTA_FATHERS_DAY = "FATHERS_DAY"
    PIANO_CTA_MOBILE_APP = "MOBILE_APP"
    PIANO_CTA_SPRING_PROMO = "SPRING_PROMO"
    PIANO_CTA_CRICKET_CAMPAIGN = "CRICKET_CAMPAIGN"
    PIANO_CTA_XMAS_2021 = "XMAS_2021"
    PIANO_CTA_COUNTRY_MUSIC_FESTIVAL = "COUNTRY_MUSIC_FESTIVAL"
    PIANO_CTA_FREE_GROCERIES = "FREE_GROCERIES"

    PIANO_CTA_VARIANTS = (
        (PIANO_CTA_DEFAULT, _("Subscribe Now")),
        (PIANO_CTA_SUMMER_SALE, _("Summer Sale")),
        (PIANO_CTA_WINTER_SPORTS, _("Winter Sports")),
        (PIANO_CTA_FOOD_MONTH, _("Food Month")),
        (PIANO_CTA_MOTHERS_DAY, _("Mothers Day")),
        (PIANO_CTA_EOFY_2021, _("EOFY 2021")),
        (PIANO_CTA_FREE_FUEL, _("Free Fuel")),
        (PIANO_CTA_FATHERS_DAY, _("Fathers Day")),
        (PIANO_CTA_MOBILE_APP, _("Mobile App")),
        (PIANO_CTA_SPRING_PROMO, _("Spring Promo")),
        (PIANO_CTA_CRICKET_CAMPAIGN, _("Cricket Campaign")),
        (PIANO_CTA_XMAS_2021, _("XMAS 2021")),
        (PIANO_CTA_COUNTRY_MUSIC_FESTIVAL, _("Country Music Festival")),
        (PIANO_CTA_FREE_GROCERIES, _("Free Groceries")),
    )

    PIANO_COMPLETE_PROFILE_ENRICHMENT_NONE = "NONE"
    PIANO_COMPLETE_PROFILE_ENRICHMENT_AGS = "AGS"
    PIANO_COMPLETE_PROFILE_ENRICHMENT_REGIONALS = "REGIONALS"
    PIANO_COMPLETE_PROFILE_ENRICHMENTS = (
        (PIANO_COMPLETE_PROFILE_ENRICHMENT_NONE, "None"),
        (PIANO_COMPLETE_PROFILE_ENRICHMENT_AGS, "Ags"),
        (PIANO_COMPLETE_PROFILE_ENRICHMENT_REGIONALS, "Regionals"),
    )

    title = "Piano"
    default_enabled = False
    piano_aid = models.CharField(blank=True, max_length=100)
    piano_api_token = models.CharField(blank=True, max_length=100)
    piano_private_key = models.CharField(blank=True, max_length=100)
    piano_shared_secret = models.CharField(blank=True, max_length=512)
    piano_site_id = models.CharField(
        verbose_name="Piano site id for composure-1-x",
        blank=True,
        max_length=100,
    )
    piano_sub_header = models.CharField(
        verbose_name=_("Subscribe Page v2 Header"), max_length=200, blank=True
    )
    piano_sub_subheader = models.CharField(
        verbose_name=_("Subscribe Page v2 Subheader"),
        max_length=200,
        blank=True,
    )
    piano_sub_colour = models.CharField(
        verbose_name=_("Subscribe Page v2 Primary Colour"),
        max_length=7,
        default="#efeff4",
    )
    piano_cta_variant = models.CharField(
        _("Subscribe CTA Variant"),
        max_length=32,
        choices=PIANO_CTA_VARIANTS,
        default=PIANO_CTA_DEFAULT,
    )
    piano_complete_profile_enrichments = models.CharField(
        _("Complete Profile Enrichment Questions"),
        max_length=16,
        choices=PIANO_COMPLETE_PROFILE_ENRICHMENTS,
        default=PIANO_COMPLETE_PROFILE_ENRICHMENT_NONE,
    )
    piano_support_premium_subscription = models.BooleanField(
        verbose_name=_("Supports Premium Subscription"), default=False
    )
    piano_support_premium_extended = models.BooleanField(
        verbose_name=_("Supports Premium Extended"),
        default=False,
        help_text=_("Include Dailymotion and Viafoura for extended premium"),
    )
    piano_premium_subscription_resources = models.TextField(
        verbose_name=_("Premium Subscription Resources"),
        blank=True,
        default="Digital Subscription",
        help_text=_(
            "Comma(,) separated resources. e.g. Digital Subscription, Premium Subscription"
        ),
    )
    piano_premium_subscription_terms = models.TextField(
        verbose_name=_("Premium Subscription Terms"),
        blank=True,
        default="",
        help_text=_(
            "Comma(,) separated term names. e.g. Premium Annual Dailies, Premium Weekly Dailies"
        ),
    )
    piano_premium_extended_subscription_resources = models.TextField(
        verbose_name=_("Premium Extended Subscription Resources"),
        blank=True,
        default="Digital Subscription",
        help_text=_(
            "Comma(,) separated resources. e.g. Digital Subscription, Premium Subscription"
        ),
    )
    piano_premium_extended_subscription_terms = models.TextField(
        verbose_name=_("Premium Extended Subscription Terms"),
        blank=True,
        default="",
        help_text=_(
            "Comma(,) separated term names. e.g. Premium Annual Dailies, Premium Weekly Dailies"
        ),
    )
    piano_beta_resource_id = models.CharField(
        verbose_name=_("Piano Beta Resource ID"), max_length=200, blank=True
    )
    registration_only = models.BooleanField(
        "Registration wall only", default=False
    )
    article_paywall_heading_text = models.CharField(
        _("Article Paywall Heading Text"),
        max_length=100,
        blank=True,
        help_text="Optional text to display in the article paywall heading",
    )
    hide_article_annual_savings_pill = models.BooleanField(
        "Hide Article Annual Savings Pill (Save %)", default=False
    )
    hide_subscriber_signposts = models.BooleanField(
        "Hide Subscriber Signposts", default=False
    )
    social_media = models.BooleanField(
        "Social Media",
        default=True,
        help_text=_("Enable social media screen in the onboarding workflow"),
    )
    enable_piano_recommendation_content = models.BooleanField(
        "Enable Piano Recommendation Content",
        default=False,
        help_text=_(
            "Enable Piano Recommendation Content. It will require Content configuration and Composure configuration"
        ),
    )
    enable_piano_recommendation_content_fullwidth = models.BooleanField(
        "Enable Piano Recommendation Content in full width",
        default=False,
        help_text=_(
            "Enable Piano Fullwidth Recommendation Content. "
            "It will require Content configuration and Composure configuration"
        ),
    )
    enable_piano_a_b_test = models.BooleanField(
        "Enable Piano A/B Testing",
        default=False,
        help_text=_(
            "It is only required to enabled when A/B Testing on the backend"
        ),
    )
    piano_support_print_bundle = models.BooleanField(
        "Site Supports Print Bundle", default=False
    )
    piano_support_login_facebook = models.BooleanField(
        "Site Supports Facebook Social Login", default=False
    )
    piano_support_login_google = models.BooleanField(
        "Site Supports Google Social Login", default=False
    )
    piano_support_login_apple = models.BooleanField(
        "Site Supports Apple Social Login", default=False
    )
    piano_support_auth_server_paywall = models.BooleanField(
        "Site Supports Auth Server Paywall", default=False
    )
    piano_support_monthly_annual_paywall = models.BooleanField(
        "Site Supports Monthly/Annual Paywall", default=False
    )


class RetentlyFeature(Feature):
    """
    Feature to enable/disable Piano feature
    """

    title = "Retently"
    default_enabled = False
    retently_track_revoked_access = models.BooleanField(
        verbose_name=_("Track Revoked Access"), default=False
    )
    retently_track_recent_signups = models.BooleanField(
        verbose_name=_("Track Recent Signups"), default=False
    )


class PhotoGalleryFeature(Feature):
    """
    Feature to enable/disable Photo gallery extra features
    """

    title = "Photo Gallery"

    photogallery_layout_choice = models.CharField(
        max_length=10,
        choices=GALLERY_LAYOUT_CHOICES,
        default=DEFAULT_GALLERY_LAYOUT,
        help_text="Gallery layout used on story gallery elements.",
        verbose_name=_("Gallery Layout"),
    )

    photogallery_choice = models.CharField(
        blank=False,
        null=False,
        max_length=5,
        choices=GALLERY_STYLES_CHOICES,
        default=DEFAULT_GALLERY_STYLE,
        help_text="Gallery used on story gallery elements.",
        verbose_name=_("Gallery Style"),
    )

    show_in_between_slide_ads = models.BooleanField(
        verbose_name=_("Show Ads in between slides"), default=False
    )
    ad_frequency = models.PositiveIntegerField(
        verbose_name=_("Ads frequency in between gallery items"),
        help_text=_("Ex: 5 will display Ads in position 5, 10, 15..."),
        blank=True,
        null=True,
    )
    show_cta = models.BooleanField(
        verbose_name=_("Show Call to action"),
        default=False,
        help_text=_("Show CTA buttons and text inside the Gallery"),
    )
    cta_slide_title = models.CharField(
        verbose_name=_("CTA slide Title"),
        max_length=70,
        blank=True,
    )
    cta_slide_description = models.CharField(
        verbose_name=_("CTA slide Description"),
        max_length=500,
        blank=True,
    )
    cta_slide_button_text = models.CharField(
        verbose_name=_("CTA slide button text"),
        max_length=40,
        blank=True,
    )
    cta_persistent_description = models.CharField(
        verbose_name=_("CTA Link Description"),
        help_text=_("Short description before link"),
        max_length=80,
        blank=True,
    )
    cta_persistent_button_text = models.CharField(
        verbose_name=_("CTA Link text"),
        max_length=40,
        blank=True,
    )
    cta_url = models.CharField(
        verbose_name=_("CTA URL"),
        max_length=500,
        blank=True,
    )


class MailFeature(Feature):
    """Feature to enable marketing emails."""

    title = "Mail"
    default_enabled = False

    class MailProviderChoices(models.IntegerChoices):
        """Supported mail providers."""

        MAILCHIMP = 0, _("Mailchimp (ACM)")
        MARKETING_CLOUD = 1, _("Marketing Cloud")
        MAILCHIMP_AGS = 2, _("Mailchimp (Ags)")
        MAILCHIMP_THE_SENIOR = 3, _("Mailchimp (The Senior)")

    provider = models.PositiveSmallIntegerField(
        _("Provider"),
        choices=MailProviderChoices.choices,
        default=MailProviderChoices.MAILCHIMP,
    )

    marketing_cloud_url = models.CharField(
        _("Marketing Cloud newsletter sign up page URL"),
        blank=True,
        max_length=500,
    )

    preference_page_id = models.PositiveIntegerField(
        _("Marketing Cloud Preference Centre Page ID"),
        blank=True,
        null=True,
    )

    support_newsletters_landing_page = models.BooleanField(
        verbose_name=_("Support Newsletters Landing Page"),
        default=False,
        help_text=_("Support the Newsletters landing page redesign"),
    )

    list_id = models.CharField(
        _("List ID"),
        blank=True,
        max_length=100,
    )

    default_interests = RenderedArrayField(
        models.CharField(max_length=64, blank=True),
        verbose_name=_("Default newsletter interests"),
        help_text="Leave blank to enable all interests",
        default=list,
        blank=True,
    )

    article_widget_heading = models.CharField(
        _("Article Widget Heading"), max_length=64, blank=True
    )

    article_widget_url = models.CharField(
        _("Article Widget URL"), max_length=128, blank=True
    )

    support_newsletters_subscribe_flow = models.BooleanField(
        verbose_name=_("Support Newsletters Subscribe Flow"),
        default=False,
        help_text=_("Support the new Newsletters subscribe flow"),
    )


class GoogleTagManagerFeature(Feature):
    """Feature to enable Google Tag Manager (GTM)."""

    title = "Google Tag Manager"
    default_enabled = False
    # GTM-xxxxxx
    container_id = models.CharField(
        verbose_name="Container ID",
        max_length=20,
        default="",
        blank=False,
        help_text="e.g. GTM-xxxxxx",
    )
    # G-XXXXXXXXXX
    measurement_id = models.CharField(
        verbose_name="Measurement ID",
        max_length=20,
        default="",
        blank=True,
        help_text="e.g. G-XXXXXXXXXX",
    )
    # `gtm_auth=xxxxxxxxxxxxxxxxxxxxxx&gtm_preview=env-xx`
    env_vars = models.CharField(
        verbose_name="Environment variables",
        max_length=60,
        default="",
        blank=True,
        help_text="Use the format `gtm_auth=...&gtm_preview=env-...`",
    )


class GoogleOptimizeManagerFeature(Feature):
    """Feature to enable Google Optimize Manager."""

    title = "Google Optimize"
    default_enabled = False
    # GTM-xxxxxx
    container_id = models.CharField(
        verbose_name="Container ID", max_length=20, default="", blank=False
    )
    ga_id = models.CharField(
        verbose_name="Google Analytics ID",
        max_length=30,
        default="",
        blank=True,
        help_text="Google Analytics ID that is associated "
        "with the Google Optimize Account",
    )

    experience = models.CharField(
        verbose_name="A/B Test Experience Name",
        max_length=64,
        default="",
        blank=True,
        help_text="Need to match with the prefix of go_experience_id "
        "in google ads manager key-value",
    )


class GoogleExtendedAccessFeature(Feature):
    """ "Feature to enable Google Extended Access."""

    title = "Google Extended Access"
    default_enabled = False
    client_id = models.CharField(
        verbose_name="Client Id",
        max_length=100,
        default="",
        blank=False,
        help_text="Google OAuth Client Id generated on GCP (credentials) "
        "for web",
    )


class AdsLazyloadManagerFeature(Feature):
    """Feature to enable google lazyload ads."""

    title = "Lazyload Google Ads"
    default_enabled = False


class DPEFeature(Feature):
    """
    Feature to enable/disable Digital Print Edition
    """

    title = "Digital Print Edition"
    default_enabled = False
    version = models.CharField(
        _("Version"),
        blank=False,
        max_length=20,
        choices=(
            ("v1", "RPL eEdition hosted (v1)"),
            ("v2", "AWS Cloud hosted (v2)"),
        ),
        default="v1",
    )
    dpe_id = models.CharField(_("DPE ID"), max_length=10, blank=True)
    dpe_publish_time = models.CharField(
        _("DPE Publish Time (when issue allowed to be viewed)"),
        max_length=4,
        validators=[
            RegexValidator(
                regex="^(?:([01]\d|2[0-3])([0-5]\d)|2400)$",
                message="Must be 4 digits in 24hr hhmm format",
                code="nomatch",
            )
        ],
        blank=False,
        default="0600",
        help_text="Use 24hr hhmm format",
    )
    dpe_howto_video_id = models.CharField(
        _("DPE How To Video ID"),
        max_length=15,
        blank=True,
        help_text="e.g. 6180993819001",
    )


class OwnLocalFeature(Feature):
    """Feature to enable/disable OwnLocal ads."""

    title = "OwnLocal"
    default_enabled = False
    has_directory = models.BooleanField(
        _("Enable Trades & Services directory"),
        default=False,
    )
    partner_id = models.CharField(_("Partner ID"), max_length=36, blank=True)


class WeatherZoneAPIFeature(Feature):
    """
    Feature to enable/disable WeatherZone API
    """

    title = "WeatherZone API"
    default_enabled = False
    user_id = models.CharField(blank=True, max_length=100)
    password = models.CharField(blank=True, max_length=100)


class BFEsovFeature(Feature):
    """
    Feature to enable/disable business feature ESOV(Equal Share of Voice)
    """

    title = "Business feature ESOV"
    default_enabled = True


class HeadlineTestFeature(Feature):
    """
    Feature to enable/disable Chartbeat headline testing
    """

    title = "Chartbeat Headline Test"
    default_enabled = False


class AMPFeature(Feature):
    """Feature to enable/disable AMP."""

    title = "AMP"
    default_enabled = False


class MobileAppFeature(Feature):
    """
    Feature to enable/disable WeatherZone API
    """

    title = "Mobile App"
    default_enabled = False
    app_store_id = models.CharField(
        verbose_name=_("App Store ID"),
        blank=True,
        max_length=100,
        help_text=_("e.g. 'newcastle-herald/id1527270296'"),
    )
    google_play_id = models.CharField(
        verbose_name=_("Google Play ID"),
        blank=True,
        max_length=100,
        help_text=_("e.g. 'com.canberratimes.app'"),
    )
    smart_banner = models.BooleanField(
        verbose_name=_("Smart Banner"),
        default=False,
        help_text=_("enable the smart banner"),
    )
    smart_banner_app_header = models.CharField(
        verbose_name=_("Smart Banner Header"),
        blank=False,
        max_length=50,
        default="Faster, easier access",
    )
    app_ios_price_info = models.CharField(
        verbose_name=_("IOS Price Information"),
        blank=True,
        max_length=100,
        default="GET - On the App Store",
        help_text=_("IOS App price information on the smart banner"),
    )
    app_android_price_info = models.CharField(
        verbose_name=_("Android Price Information"),
        blank=True,
        max_length=100,
        default="GET - In Google Play",
        help_text=_("Android App price information on the smart banner"),
    )
    ios_app_id = models.CharField("iOS App ID", max_length=100, blank=True)
    android_fingerprints = models.CharField(
        "Android SHA256 certificate fingerprints",
        help_text="Comma separated",
        max_length=480,
        blank=True,
    )
    branch_io_public_key = models.CharField(
        _("Branch.io Public Key"),
        default="",
        blank=True,
        max_length=100,
        help_text=(
            "Find Your Branch Key: In the Branch Dashboard, go to "
            "Account Settings > Profile. Your Branch Key will be listed there"
        ),
    )


class PushNotificationFeature(Feature):
    """Feature to enable/disable Airship Push Notification."""

    title = "Push Notification"
    default_enabled = False
    vapid_public_key = models.CharField(
        verbose_name=_("Web Push Key"), blank=True, max_length=200
    )
    app_key = models.CharField(
        verbose_name=_("Airship App Key"), blank=True, max_length=200
    )
    app_token = models.CharField(
        verbose_name=_("Airship Bearer Token"), blank=True, max_length=200
    )
    website_push_id = models.CharField(
        verbose_name=_("Airship Web Push ID"),
        max_length=200,
        blank=True,
        help_text="Safari Website Push ID",
    )
    notification_icon = models.URLField(
        verbose_name=_("Default Image Url"),
        blank=True,
        help_text=_("The default url of the notficaton icon"),
    )
    display_threshold = models.PositiveIntegerField(
        default=2,
        verbose_name=_("Display threshold"),
        help_text=_(
            "Display soft ask notification after viewing threshold articles"
        ),
    )
    ask_again = models.PositiveIntegerField(
        default=7,
        verbose_name=_("Ask again"),
        help_text=_(
            "Time to display airship soft ask again if deny to accept notification. Unit of time is day"
        ),
    )


class PuzzleFeature(Feature):
    """Feature to enable/disable puzzle."""

    title = "Puzzle"
    default_enabled = False
    logo = models.URLField(
        verbose_name=_("Default Puzzle Logo"),
        blank=True,
        help_text=_("The default url of the puzzle logo"),
    )
    subscribers_only = models.BooleanField(
        default=True,
        verbose_name=_("Subscribers Only"),
        help_text="Make puzzles available only for subscribers",
    )
    crossword = models.URLField(verbose_name=_("Crossword"), blank=True)
    sudoku = models.URLField(verbose_name=_("Sudoku"), blank=True)
    ultimate_trivia = models.URLField(
        verbose_name=_("Ultimate Trivia"), blank=True
    )
    cryptic_crossword = models.URLField(
        verbose_name=_("Cryptic Crossword"), blank=True
    )
    word_search = models.URLField(verbose_name=_("Word Search"), blank=True)
    code_cracker = models.URLField(verbose_name=_("Code Cracker"), blank=True)
    wheel_words = models.URLField(verbose_name=_("Wheel Words"), blank=True)


class CKEditorFeature(Feature):
    """Feature to enable/disable CKEditor."""

    title = "CKEditor"
    default_enabled = False


FACEBOOK_NEWS_SCOPE_CHOICES = tuple(
    [
        ("news_tab_dev_env", "Sandbox"),
    ]
    + (
        [
            ("news_tab", "Production"),
        ]
        if os.environ.get("DJANGO_ENVIRONMENT") == "production"
        else []
    )
)


class FacebookNewsFeature(Feature):
    """Feature to enable/disable Facebook News Indexing API."""

    title = "Facebook News"
    default_enabled = False
    app_id = models.TextField(verbose_name=_("Facebook App ID"), blank=True)
    secret = models.TextField(
        verbose_name=_("Facebook App Secret"), blank=True
    )
    api_token = models.TextField(
        verbose_name=_("Facebook App Token"), blank=True
    )
    scope = models.CharField(
        verbose_name=_("Facebook Indexing Scope"),
        blank=True,
        choices=FACEBOOK_NEWS_SCOPE_CHOICES,
        default="",
        max_length=50,
    )
    automatic = models.BooleanField(verbose_name=("Automatic"), default=True)


class MicrosoftClarityFeature(Feature):
    """Feature to enable/disable Microsoft Clarity."""

    title = "Microsoft Clarity"
    default_enabled = False
    clarity_id = models.CharField(
        verbose_name=_("Microsoft Clarity Client ID"),
        blank=True,
        max_length=50,
    )


class RoyMorganAnalyticsFeature(Feature):
    """Feature to enable/disable Roy Morgan Analytics."""

    title = "Roy Morgan Analytics"
    default_enabled = False
    client_id = models.CharField(
        verbose_name=_("Roy Morgan Client ID (Individual)"),
        help_text="`u` parameter",
        blank=True,
        max_length=20,
    )
    website_id = models.CharField(
        verbose_name=_("Roy Morgan Website ID (Individual)"),
        help_text="`ca` parameter",
        blank=True,
        max_length=20,
    )
    publisher_id = models.CharField(
        verbose_name=_("Roy Morgan Publisher ID (Individual)"),
        help_text="`a` parameter",
        blank=True,
        max_length=20,
    )
    client_id_2 = models.CharField(
        verbose_name=_("Roy Morgan Client ID (Group)"),
        help_text="`u` parameter",
        blank=True,
        max_length=20,
    )
    website_id_2 = models.CharField(
        verbose_name=_("Roy Morgan Website ID (Group)"),
        help_text="`ca` parameter",
        blank=True,
        max_length=20,
    )
    publisher_id_2 = models.CharField(
        verbose_name=_("Roy Morgan Publisher ID (Group)"),
        help_text="`a` parameter",
        blank=True,
        max_length=20,
    )
    client_id_3 = models.CharField(
        verbose_name=_("Roy Morgan Client ID (Network)"),
        help_text="`u` parameter",
        blank=True,
        max_length=20,
    )
    website_id_3 = models.CharField(
        verbose_name=_("Roy Morgan Website ID (Network)"),
        help_text="`ca` parameter",
        blank=True,
        max_length=20,
    )
    publisher_id_3 = models.CharField(
        verbose_name=_("Roy Morgan Publisher ID (Network)"),
        help_text="`a` parameter",
        blank=True,
        max_length=20,
    )


class IpsosIrisAnalyticsFeature(Feature):
    """Feature to enable/disable Ipsos Iris Analytics."""

    title = "Ipsos Iris Analytics"
    default_enabled = False
    homepage_section_id = models.CharField(
        verbose_name=_("Ipsos Setion ID (Homepage)"),
        blank=True,
        max_length=8,
    )
    default_section_id = models.CharField(
        verbose_name=_("Ipsos Section ID (Other)"),
        help_text="default section ID",
        blank=True,
        max_length=8,
    )
    news_section_id = models.CharField(
        verbose_name=_("Ipsos Section ID (News)"),
        blank=True,
        max_length=8,
    )
    classifieds_section_id = models.CharField(
        verbose_name=_("Ipsos Section ID (Classifieds)"),
        blank=True,
        max_length=8,
    )
    jobs_section_id = models.CharField(
        verbose_name=_("Ipsos Section ID (Jobs)"),
        blank=True,
        max_length=8,
    )
    sport_section_id = models.CharField(
        verbose_name=_("Ipsos Section ID (Sport)"),
        blank=True,
        max_length=8,
    )
    comment_section_id = models.CharField(
        verbose_name=_("Ipsos Section ID (Comment)"),
        blank=True,
        max_length=8,
    )
    whatson_section_id = models.CharField(
        verbose_name=_("Ipsos Section ID (What's On)"),
        blank=True,
        max_length=8,
    )


reversion.register(Site)


class StrapFeature(Feature):
    """Feature to configure Strap features."""

    title = "Strap Feature"
    default_enabled = False
    share_with_rev = models.BooleanField(
        verbose_name=_("Share With Real Estate View"),
        help_text=_("Includes Masthead articles along with National"),
        default=False,
    )


class DailymotionFeature(Feature):
    """Feature to configure Dailymotion features."""

    title = "Dailymotion Feature"
    default_enabled = True
    player_id = models.CharField(
        verbose_name=_("Dailymotion Player id"),
        max_length=50,
        null=True,
        blank=True,
    )
    player_id_no_autoplay = models.CharField(
        verbose_name=_("Dailymotion Player id with autoplay disabled"),
        max_length=50,
        blank=True,
    )
    player_id_video_shorts = models.CharField(
        verbose_name=_("Dailymotion Player id with video shorts"),
        max_length=50,
        blank=True,
    )
    player_id_for_looper_videos = models.CharField(
        verbose_name=_("Dailymotion Player id for looper videos"),
        max_length=50,
        blank=True,
    )
    player_id_for_explore_travel_articles = models.CharField(
        verbose_name=_("Dailymotion Player id for Explore Travel articles"),
        max_length=50,
        blank=True,
    )
    owner_id = models.CharField(
        verbose_name=_("Dailymotion Owner id"),
        max_length=200,
        blank=True,
    )
    default_playerlist_id = models.CharField(
        verbose_name=_("Default playlist id"),
        help_text=_("Default dailymotion video shorts playlist id"),
        max_length=20,
        blank=True,
    )
    pip_enabled_mobile = models.BooleanField(
        verbose_name=_("Picture in picture enabled for mobile"),
        default=False,
    )
    pip_enabled_desktop = models.BooleanField(
        verbose_name=_("Picture in picture enabled for desktop"),
        default=False,
    )


class EMagsFeature(Feature):
    """
    Feature to enable/disable E-Mags
    """

    title = "E-Mags"
    default_enabled = False


class LiveRampFeature(Feature):
    """LiveRamp ATS analytics."""

    title = "LiveRamp"


class WeatherFeature(Feature):
    """
    Feature to enable/disable weather
    """

    title = "Weather"
    default_enabled = True


class AsyncDeferThirdPartyScriptsFeature(Feature):
    """Feature to use async or defer in Monaco scripts."""

    title = "Async/Defer 3rd Party Scripts"
    default_enabled = False


class SportsHubSponsorFeature(Feature):
    """Feature to enable/disable sponsorship in sports hub"""

    title = "Sports Hub Sponsor"
    default_enabled = False

    SPORT_CHOICES = (
        ("afl", "AFL"),
        ("cricket", "Cricket"),
        ("nrl", "NRL"),
        ("a-league", "A-League"),
        ("t20", "T20"),
        ("basketball", "Basketball"),
        ("hockey", "Hockey"),
        ("netball", "Netball"),
        ("oneDay", "One Day Cricket"),
    )

    sponsor_data = PrettyJSONField(
        verbose_name=_("Sports Sponsor Data"),
        default=dict,
        blank=True,
        help_text=_("Add sponsor data for each sport"),
        validators=[validate_sports_sponsor_data],
    )
    featured_sport_sponsor = models.CharField(
        _("Featured Sport Sponsor"),
        help_text="""
        If selected sport has a sponsor data, it will be used on 
        general sport index pages e.g. /sport or /sport/local-sport
        """,
        max_length=20,
        blank=True,
        choices=SPORT_CHOICES,
    )


class CustomerDataPlatformFeature(Feature):
    """
    Feature to enable/disable Customer Data Platform
    """

    title = "Customer Data Platform"
    default_enabled = False


class DailymotionAutoPauseFeature(Feature):
    """Feature to enable/disable auto pause when a user scrolls out of dailymotion player's viewport"""

    title = "Dailymotion Auto Pause"
    default_enabled = False


class ReCaptchaV3Feature(Feature):
    """
    Feature to configure reCAPTCHA v3 thresholds and secrets for authentication use
    """

    title = "ReCAPTCHA v3"
    default_enabled = False
    project_id = models.CharField(blank=True, max_length=100)
    public_api_key = models.CharField(
        blank=True, max_length=100, help_text=_("Site key")
    )
    private_api_key = models.CharField(
        blank=True, max_length=100, help_text=_("Authentication key")
    )
    default_score_threshold = models.DecimalField(
        max_digits=2,
        decimal_places=1,
        default=0.5,
        help_text=_("Default threshold for non-specified actions (0.0-1.0)"),
    )
    registration_score_threshold = models.DecimalField(
        max_digits=2,
        decimal_places=1,
        default=0.5,
        help_text=_("Threshold for registration actions (0.0-1.0)"),
    )


class ChaperoneSiteFeature(Feature):
    """Feature to configure the chaperone (parent) site."""

    title = "Chaperone Site"
    default_enabled = False
    chaperone_site = models.ForeignKey(
        Settings,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="Chaperone Site",
        help_text="The site to use as the chaperone (parent) site.",
    )


class CommunityShareFormFeature(Feature):
    """Feature to enable/disable access to community share content form"""

    title = "Community Share Content Form"
    default_enabled = False


class TaboolaFeature(Feature):
    """Feature to enable Taboola"""

    title = "Taboola"
    default_enabled = False
    publisher_id = models.CharField(
        verbose_name=_("Publisher ID"),
        blank=True,
        max_length=100,
        help_text=_("ID is provided by Taboola"),
    )


class CommunityRecirculationFeature(Feature):
    """
    Feature to enable/disable Community Recirculation
    """

    title = "Community Recirculation"
    default_enabled = False


class AdFixusFeature(Feature):
    """
    Feature to configure support for AdFixus tracking
    """

    title = "AdFixus"
    default_enabled = False
    version = models.CharField(blank=True, max_length=16)
    version_url = models.CharField(blank=True, max_length=16)
    license_key = models.CharField(blank=True, max_length=64)


class SkimlinksFeature(Feature):
    """Feature to enable/disable Skimlinks"""

    title = "Skimlinks"

    key = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_("Skimlinks Unique Key"),
        help_text=_(
            "This is the unique code within the provided Skimlinks Script URL. The url usually is in the form 'https://s.skimresources.com/js/{KEY}.skimlinks.js' where {KEY} is the unique key."
        ),
    )


class UgcFeature(Feature):
    """
    Feature to configure UGC
    """

    title = "User Generated Content"
    default_enabled = True
    use_region = models.BooleanField(
        verbose_name=_("Use region"),
        default=False,
        help_text=_("Use events, photos and stories in region"),
    )
    show_share_story = models.BooleanField(
        verbose_name=_("Show share story"),
        default=False,
        help_text=_("Show share story card on contribute page"),
    )
    cta_share_story = models.CharField(
        verbose_name=_("Share story CTA"),
        max_length=100,
        blank=True,
        help_text=_(
            "CTA path (e.g., /notice-board/share-story/) for share story card on contribute page."
        ),
    )


class ExploreTravelRecirculationFeature(Feature):
    """
    Feature to configure Explore Travel Recirculation Widget
    """

    title = "Explore Travel Recirculation"
    default_enabled = True
    latest_story_widget_story_list = models.ForeignKey(
        "stories.StoryList",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Latest Story Widget Story List"),
        help_text=_(
            "The story list to use for the latest story widget in Story pages."
        ),
        related_name="explore_travel_latest_story_widget",
        db_column="exploretravelrecirculationfeature_ltst_story_widget_story_list_id",
    )
    latest_story_widget_story_limit = models.IntegerField(
        verbose_name=_("Latest Story Widget Story Limit"),
        default=6,
        help_text=_(
            "The number of stories to display in the latest story widget."
        ),
        db_column="exploretravelrecirculationfeature_ltst_story_widget_limit",
    )
    most_popular_story_widget_story_list = models.ForeignKey(
        "stories.StoryList",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Most Popular Story Widget Story List"),
        help_text=_(
            "The story list to use for the most popular story widget in Story pages."
        ),
        related_name="explore_travel_most_popular_story_widget",
        db_column="exploretravelrecirculationfeature_mpst_story_widget_story_list_id",
    )
    most_popular_story_widget_story_limit = models.IntegerField(
        verbose_name=_("Most Popular Story Widget Story Limit"),
        default=10,
        help_text=_(
            "The number of stories to display in the most popular story widget."
        ),
        db_column="exploretravelrecirculationfeature_mpst_story_widget_limit",
    )


class TravelArticleTemplateFeature(Feature):
    """
    Feature to enable/disable Explore Travel Article Template
    """

    title = "Travel Article Template"
    default_enabled = False


class NorkonLiveblogFeature(Feature):
    """Feature to enable/disable for Norkon Liveblog."""

    title = "Norkon liveblog"

    asset_version = models.CharField(
        "Asset Version", max_length=12, blank=False, default="6.1.1"
    )


class UserBookmarksFeature(Feature):
    """
    Feature to configure user bookmarks
    """

    title = "User Bookmarks"
    default_enabled = False
