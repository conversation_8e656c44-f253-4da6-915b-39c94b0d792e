from typing import Union

from django.conf import settings
from django.contrib import admin
from django.contrib.auth.decorators import login_required
from django.contrib.sitemaps.views import sitemap
from django.urls import URLPattern, URLResolver, include, re_path
from django.views.generic import RedirectView, TemplateView

from suzuka.ads.views import AdDataLayerView
from suzuka.business.sitemaps import BusinessSitemap
from suzuka.caches.manage.views import clear_memcache, manage_caches
from suzuka.classifieds.manage.views import (
    delete_classifieds,
    manage_classifieds,
)
from suzuka.classifieds.sitemaps import ClassifiedsSitemap

# TODO: Removing as function not working now.
# from suzuka.debug_views import debug
from suzuka.conf.views import (
    sitemap_index,
    sitemap_reporting,
    sitemap_reporting_refresh,
    sitemap_year_index,
    sitemap_yearmonthday,
)
from suzuka.exploretravel.sitemaps import ExploreTravelRootSitemap
from suzuka.global_features.views import manage_features
from suzuka.helpcentre.sitemaps import HelpCentreRootSitemap
from suzuka.notification.manage.views import channel_view
from suzuka.pages.editing.views import manage_content_redirect
from suzuka.pages.sitemaps import PageSitemap
from suzuka.pages.views import RobotsView, megamenu_feed
from suzuka.stories.feeds.application import MostPopularAtomFeed
from suzuka.stories.feeds.syndication import (
    DPESyndication,
    GoogleNewsRundownSyndication,
    MarketingSyndication,
    MostPopularSyndication,
    PageSyndication,
)
from suzuka.stories.manage.views import (
    group_pinned_stories,
    group_pinned_stories_pk,
)

# Sitemaps
from suzuka.stories.sitemaps import StorySitemap, StoryYearMonthDaySitemap

admin.autodiscover()

sitemaps = {
    "classifieds": ClassifiedsSitemap,
    "sections": PageSitemap,
    "news": StorySitemap,
    "business": BusinessSitemap,
    "explore": ExploreTravelRootSitemap,
    "helpcentre": HelpCentreRootSitemap,
}

urlpatterns: list[Union[URLResolver, URLPattern]] = []

if settings.DEBUG and "debug_toolbar" in settings.INSTALLED_APPS:
    import debug_toolbar

    urlpatterns += [
        re_path(r"^__debug__/", include(debug_toolbar.urls)),
    ]

urlpatterns += [
    re_path(r"^shared_login/", include("shared_login.login_consumer.urls")),
    re_path(
        r"^shared_login/webhook/",
        include("shared_login.login_consumer.webhooks.urls"),
    ),
    re_path(r"^cognito/", include("newsnow_cognito.consumer.urls")),
    # Admin URLs
    re_path(r"^admin/doc/", include("django.contrib.admindocs.urls")),
    re_path(r"^admin/", admin.site.urls),
    # Debug URL
    # TODO: Removing as function not working now.
    # re_path(r'^_debug$', debug),
    # Manage URLs
    re_path(r"^manage/$", RedirectView.as_view(url="/manage/sites/")),
    re_path(r"^manage/sites/", include("suzuka.conf.urls")),
    re_path(
        r"^manage/group_pinning/$",
        group_pinned_stories,
        name="group_pinned_stories",
    ),
    re_path(
        r"^manage/group_pinning/(?P<pk>\d+)/$",
        group_pinned_stories_pk,
        name="group_pinned_stories_pk",
    ),
    re_path(r"^manage/caches/$", manage_caches, name="manage_caches"),
    re_path(
        r"^manage/caches/clear_memcache/$",
        clear_memcache,
        name="clear_memcache",
    ),
    re_path(
        r"^manage/classifieds/$", manage_classifieds, name="manage_classifieds"
    ),
    re_path(r"^manage/features/$", manage_features, name="manage_features"),
    re_path(
        r"^manage/classifieds/(?P<pk>\d+)/$",
        delete_classifieds,
        name="delete_classifieds",
    ),
    re_path(r"^manage/mailinglists/", include("suzuka.mailinglists.urls")),
    re_path(r"^manage/outages/", include("suzuka.outages.manage.urls")),
    re_path(r"^manage/emags/", include("suzuka.emags.manage.urls")),
    re_path(r"^manage/sports-hub/", include("suzuka.sports_hub.manage.urls")),
    re_path(
        r"^manage/paywall-campaigns/",
        include("suzuka.subscriptions.manage.urls"),
    ),
    re_path(
        r"^manage/(?P<domain>.*)/notification/",
        include("suzuka.notification.manage.urls"),
    ),
    re_path(
        r"^manage/(?P<domain>.*)/settings/", include("suzuka.conf.manage.urls")
    ),
    re_path(
        r"^manage/(?P<domain>.*)/storylists/",
        include("suzuka.stories.manage.urls"),
    ),
    re_path(
        r"^manage/(?P<domain>.*)/classifiedlists/",
        include("suzuka.classifieds.manage.urls"),
    ),
    re_path(
        r"^manage/(?P<domain>.*)/ugclists/",
        include("suzuka.ugc.manage.urls"),
    ),
    re_path(
        r"^manage/(?P<domain>.*)/newsletters/",
        include("suzuka.mailinglists.manage.urls"),
    ),
    re_path(
        r"^manage/(?P<domain>.*)/subscriptions/",
        include("suzuka.targetlists.urls"),
    ),
    re_path(
        r"^manage/(?P<domain>.*)/pages/", include("suzuka.pages.manage.urls")
    ),
    re_path(
        r"^manage/(?P<domain>.*)/menu/$",
        RedirectView.as_view(pattern_name="page_list"),
    ),
    re_path(
        r"^manage/content/$",
        login_required(manage_content_redirect),
        name="manage_content",
    ),
    # Live-editing urls.
    re_path(r"^edit/", include("suzuka.pages.editing.urls")),
    # Public URLs that form the site.
    re_path(
        r"^internal/tv-guide/$",
        TemplateView.as_view(template_name="tvguide_iframe.html"),
        name="tv_guide",
    ),
    re_path(
        r"^search/$",
        AdDataLayerView.as_view(template_name="search.html"),
        name="search",
    ),
    re_path(r"^rss\.xml$", PageSyndication(), {"url": "news"}),
    re_path(
        r"^rss-marketing\.xml$",
        MarketingSyndication(),
        {"url": "news"},
    ),
    re_path(r"^rss-popular\.xml$", MostPopularSyndication()),
    re_path(r"^rss-dpe\.xml$", DPESyndication()),
    re_path(r"^atom-popular\.xml$", MostPopularAtomFeed()),
    re_path(
        r"^atom-g-rundown\.xml$",
        GoogleNewsRundownSyndication(),
        {"url": "g-rundown"},
    ),
    re_path(r"^robots\.txt$", RobotsView.as_view()),
    re_path(
        r"^analytics\.txt$",
        TemplateView.as_view(
            template_name="analytics.txt", content_type="text/plain"
        ),
    ),
    re_path(
        r"^crossdomain\.xml$",
        TemplateView.as_view(
            template_name="crossdomain.xml", content_type="text/xml"
        ),
    ),
    re_path(
        r"^i-phone\.xml$",
        TemplateView.as_view(
            template_name="i-phone.xml", content_type="text/xml"
        ),
    ),
    re_path(
        r"^.well-known/apple-developer-merchantid-domain-association$",
        TemplateView.as_view(
            template_name="apple-developer-merchantid-domain-association",
            content_type="text/plain",
        ),
    ),
    # google Web Master View
    re_path(
        r"^google22326b768d351096\.html$",
        TemplateView.as_view(template_name="google22326b768d351096.html"),
    ),
    re_path(
        r"^push-worker\.js$",
        TemplateView.as_view(
            template_name="push-worker.js", content_type="text/javascript"
        ),
    ),
    re_path(r"^megamenu\.json$", megamenu_feed, name="megamenu_feed"),
    # Classifieds has its own sitemap so it must be first
    re_path(r"^", include("suzuka.classifieds.urls", namespace="classifieds")),
    # Sitemaps
    re_path(r"^sitemap\.xml$", sitemap_index, {"sitemaps": sitemaps}),
    re_path(
        r"^sitemap-news\.xml$",
        sitemap,
        {
            "section": "news",
            "sitemaps": {"news": StorySitemap},
            "template_name": "stories/sitemap_news.xml",
        },
        name="news_sitemap",
    ),
    re_path(
        r"^sitemap-(?P<year>2[0-9]{3})\.xml$",
        sitemap_year_index,
        {"sitemaps": sitemaps},
    ),
    re_path(
        r"^sitemap-(?P<yearmonthday>2[0-9]{3}[0-2][0-9][0-3][0-9])\.xml$",
        sitemap_yearmonthday,
        {
            "section": "news",
            "sitemaps": {"news": StoryYearMonthDaySitemap},
            "template_name": "stories/sitemap_yearmonthday.xml",
        },
    ),
    re_path(
        r"^sitemap-(?P<section>.+)\.xml$", sitemap, {"sitemaps": sitemaps}
    ),
    # public sitemap reporting
    re_path(
        r"^internal/sitemap-reporting/$",
        sitemap_reporting,
        name="sitemap_reporting",
    ),
    re_path(
        r"^internal/sitemap-reporting/refresh/$",
        sitemap_reporting_refresh,
        name="sitemap_reporting_refresh",
    ),
    # create notification channel
    re_path(r"^channel/create/$", channel_view, name="channel_view"),
    re_path(r"^profile/", include("suzuka.authors.urls")),
    re_path(r"^business/", include("suzuka.business.urls")),
    re_path(r"^localbusiness/", include("suzuka.business.old_urls")),
    re_path(r"^api/", include("suzuka.pagesapi.urls")),
    re_path(r"^api/farmbuy/", include("suzuka.farmbuy.urls")),
    re_path(r"^", include("suzuka.comments.urls")),
    re_path(r"^", include("suzuka.real_estate.urls")),
    re_path(r"^", include("suzuka.ads.urls")),
    re_path(r"^", include("suzuka.stories.urls")),
    re_path(r"^", include("suzuka.ugc.urls")),
    re_path(r"^", include("suzuka.localads.urls")),
    re_path(r"^", include("suzuka.subscriptions.urls")),
    re_path(r"^", include("suzuka.auctions.urls")),
    re_path(r"^", include("suzuka.sports_hub.urls")),
    re_path(r"^", include("suzuka.sports_results.urls")),
    re_path(r"^", include("suzuka.dailymotion.urls")),
    re_path(r"^", include("suzuka.exploretravel.urls")),
    re_path(r"^", include("suzuka.weather.urls")),
    re_path(r"^", include("suzuka.helpcentre.urls")),
    re_path(r"^", include("suzuka.pages.urls")),
]
