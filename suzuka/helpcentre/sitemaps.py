import logging
from datetime import timed<PERSON><PERSON>

from django.conf import settings
from django.urls import reverse

from suzuka.conf.sites import current_site
from suzuka.helpcentre import HELPCENTRE_ROOT_TAG
from suzuka.helpcentre.utils import (
    get_helpcentre_topic,
    get_root_page,
    get_topic_pages,
    is_helpcentre_topic,
)
from suzuka.stories.settings import (
    STORY_SITEMAP_CACHE_TIMEOUT,
)
from suzuka.stories.sitemaps import StorySitemap
from suzuka.stories.utils import (
    cache_get,
    cache_set,
    get_story_slug,
    story_api,
)
from suzuka.targetlists.utils import get_target_list_ids

HELPCENTRE_STORIES_SITEMAP_CACHE_KEY = "helpcentre-stories-sitemap-%s"

logger = logging.getLogger("suzuka.helpcentre.sitemaps")


class HelpCentreRootSitemap(StorySitemap):
    def items(self):
        site = current_site()

        if site is None:
            return ()

        cache_key = HELPCENTRE_STORIES_SITEMAP_CACHE_KEY % site.id
        items = cache_get(cache_key)
        if items is None:
            filters = {
                "limit": 1000,
                "organization": settings.SUPPORT_TEAM_ORG_ID,
                "target_lists": get_target_list_ids(site.id),
                "publishable": True,
                "tags": HELPCENTRE_ROOT_TAG,
            }
            try:
                stories = story_api().story.get(**filters)["objects"]
            except Exception as err:
                logger.exception(err)
                return ()
            items = []
            topic_pages = get_topic_pages()
            missing_topic_ids = []
            for story in stories:
                if is_helpcentre_topic(story, topic_pages):
                    items.append(story)
                else:
                    missing_topic_ids.append(story.id)
            if missing_topic_ids:
                logger.warning(
                    "No topic found for %d Help Centre stories on site %s; first 20 IDs: %s",
                    len(missing_topic_ids),
                    site.id,
                    missing_topic_ids[:20],
                )

            cache_set(cache_key, items, STORY_SITEMAP_CACHE_TIMEOUT)
        return items

    def location(self, story):
        topic = get_helpcentre_topic(story, {"page": get_root_page()})
        if not topic:
            return None

        kwargs = {
            "story_id": story.id,
            "story_slug": get_story_slug(story),
            "topic": topic,
        }
        return reverse("helpcentre_story_detail", kwargs=kwargs)

    def changefreq(self, story):
        return (
            "daily"
            if self.page_age(story) < timedelta(hours=48)
            else "monthly"
        )

    def priority(self, story):
        return 0.4
