from typing import Optional

from django.shortcuts import get_object_or_404
from django.urls import reverse

from suzuka.conf.sites import current_site
from suzuka.helpcentre import HELPCENTRE_ROOT_TAG, ROOT_PAGE_URL
from suzuka.stories.utils import get_story_slug


def is_helpcentre_story(story) -> bool:
    return HELPCENTRE_ROOT_TAG in getattr(story, "tags", [])


def get_root_page():
    from suzuka.pages.models import Page

    return get_object_or_404(
        Page,
        url=ROOT_PAGE_URL,
        sites=current_site(),
        draft=False,
        accessible=True,
    )


def _extract_topic_from_page_url(url: Optional[str]) -> Optional[str]:
    """Extract topic from page URL like 'help-centre/digital-access' -> 'digital-access'"""
    if not url:
        return None
    url_parts = url.strip("/").split("/")
    if len(url_parts) == 2 and url_parts[0] == ROOT_PAGE_URL:
        return url_parts[1]
    return None


def _story_matches_page_story_list(page, story) -> bool:
    if not page.story_list or not page.story_list.tags:
        return False

    tags_list = [
        tag.strip() for tag in page.story_list.tags.split(",") if tag.strip()
    ]

    return any(
        story_list_tag.startswith(f"{HELPCENTRE_ROOT_TAG}-")
        and story_list_tag in story.tags
        for story_list_tag in tags_list
    )


def _check_page_for_topic_match(page, story) -> Optional[str]:
    if _story_matches_page_story_list(page, story):
        return _extract_topic_from_page_url(page.url)
    return None


def is_helpcentre_topic(story, topic_pages) -> bool:
    for topic_page in topic_pages:
        if _story_matches_page_story_list(topic_page, story):
            return True
    return False


def get_topic_pages():
    from suzuka.pages.utils import pull_sub_pages

    root_page = get_root_page()
    return pull_sub_pages(root_page, use_current_site=False)


def get_helpcentre_topic(
    story, context: Optional[dict] = None
) -> Optional[str]:
    if not context:
        return None

    if not (current_page := context.get("page")) or not getattr(
        current_page, "url", None
    ):
        return None

    if current_page.url == ROOT_PAGE_URL:
        if topic_pages := get_topic_pages():
            for topic_page in topic_pages:
                if topic := _check_page_for_topic_match(topic_page, story):
                    return topic
    else:
        # in topic page
        if topic := _check_page_for_topic_match(current_page, story):
            return topic

    return None


def get_helpcentre_story_url(story, context: Optional[dict] = None):
    if not is_helpcentre_story(story):
        return None

    topic = (
        context and context.get("helpcentre_topic")
    ) or get_helpcentre_topic(story, context)
    if not topic:
        return None

    story_slug = (context and context.get("story_slug")) or get_story_slug(
        story
    )

    kwargs = {
        "topic": topic,
        "story_slug": story_slug,
        "story_id": story.id,
    }
    return reverse("helpcentre_story_detail", kwargs=kwargs)
