"""Utility functions for subscriptions."""

import base64
import json
import logging
from typing import Any, Optional

import requests
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from django.conf import settings
from django.contrib.sites.models import Site

from suzuka.conf.models import Settings
from suzuka.conf.sites import get_site_prefix
from suzuka.decorators import retry
from suzuka.pages.logos import logo_svg_urls
from suzuka.stories import utils
from suzuka.stories.utils import search_authors
from suzuka.subscriptions.models import (
    CLUSTER_SITE_CACHE_TIMEOUT,
    ClusterMember,
    get_cluster_site_cache_key,
)

DEFAULT_CLUSTER_ID_FOR_SQS = "default"
logger = logging.getLogger(__name__)
PIANO_DELIMITER = "~~~"
crypto_backend = default_backend()


def unpad(decrypted: str) -> str:
    """Remove padding junk from a decrypted string."""
    return decrypted[: -ord(decrypted[len(decrypted) - 1 :])]


def decrypt_piano_data(private_key: str, data: str) -> str:
    """
    Decrypt Piano data encrypted with the site's private key.

    Ported from Piano's PHP SDK v2.12.
    """
    try:
        pos = data.rindex(PIANO_DELIMITER)
    except ValueError:
        pass
    else:
        if pos > 0:
            data = data[0:pos]

    # Fix incorrect padding of base64 string
    data += "=" * ((4 - len(data) % 4) % 4)
    data_bytes = base64.urlsafe_b64decode(data)

    if len(private_key) > 32:
        private_key = private_key[0:32]
    else:
        private_key = private_key.rjust(32, "X")

    private_key_bytes = private_key.encode("utf-8")

    cipher = Cipher(
        algorithms.AES(private_key_bytes), modes.ECB(), backend=crypto_backend
    )

    decryptor = cipher.decryptor()

    data_bytes = decryptor.update(data_bytes) + decryptor.finalize()

    # If extra padding was added, strip junk data
    data = unpad(data_bytes.decode("utf8"))

    return data


def encrypt_piano_data(private_key: str, data: str) -> str:
    json_data = json.dumps(data)
    # Pad to block size of 16
    json_data += chr(16 - len(json_data) % 16) * (16 - len(json_data) % 16)

    if len(private_key) > 32:
        private_key = private_key[0:32]
    else:
        private_key = private_key.rjust(32, "X")

    private_key_bytes = private_key.encode("utf-8")

    cipher = Cipher(
        algorithms.AES(private_key_bytes), modes.ECB(), backend=crypto_backend
    )

    encryptor = cipher.encryptor()

    encrypted = (
        encryptor.update(json_data.encode("utf8")) + encryptor.finalize()
    )

    return (
        base64.b64encode(encrypted) + "~~~uselesspostfix".encode("utf8")
    ).decode("utf-8")


PUBLICATION_AID_CACHE_TIMEOUT = 24 * 60 * 60  # 24 hours
PUBLICATION_AID_CACHE_KEY = "publication-aid"


def update_publication_aid_dict():
    sites = list(
        Settings.objects.filter(
            visible=True,
            domain__contains="www",
            features__pianofeature_enabled=True,
            pianofeature_piano_aid__isnull=False,
        ).values_list("pianofeature_piano_aid", "name")
    )

    if sites:
        publication_aid_dict = dict(sites)
        utils.cache_set(
            PUBLICATION_AID_CACHE_KEY,
            publication_aid_dict,
            timeout=PUBLICATION_AID_CACHE_TIMEOUT,
        )
        return publication_aid_dict
    else:
        return None


def get_publication_aid_dict():
    publication_aid_dict = utils.cache_get(PUBLICATION_AID_CACHE_KEY)

    if publication_aid_dict is not None:
        return publication_aid_dict
    else:
        return update_publication_aid_dict()


def cluster_summary_for_site(site: Site, use_cache=True):
    def promoted_first(site):
        return bool(org_hierarchy.get(site[0]))

    cache_key = get_cluster_site_cache_key(site)
    summary = utils.cache_get(cache_key) if use_cache else None

    if summary is None:
        site_org = site.settings.organization
        member = (
            ClusterMember.objects.select_related("cluster")
            .filter(organization=site_org)
            .order_by("-is_primary")
            .first()
        )

        if not member:
            return

        content = member.content or member.cluster.content
        cluster_name = member.cluster.name
        cluster_id = member.cluster.id

        domain_prefix = get_site_prefix(site)
        org_hierarchy = member.cluster.organization_hierarchy
        member_promoted_org = org_hierarchy.pop(member.organization, None)

        # Note: To allow both beta and www clusters, sites selected will
        # match the prefix (www/beta) of the current site.

        sites = list(
            Site.objects.select_related("settings")
            .filter(
                domain__startswith=domain_prefix,
                settings__visible=True,
                settings__organization__in=org_hierarchy,
            )
            .values_list(
                "settings__organization",
                "settings__static_dir",
                "domain",
                "settings__name",
            )
            .order_by("settings__name")
        )

        # Sort the sites alphabetically by name but put non-promoted (eg. dailies) first.

        sites.sort(key=promoted_first)

        summary_sites = [
            {
                "domain": site[2],
                "name": site[3],
                "logo_svg_square": logo_svg_urls(
                    {
                        "STATIC_SITE_URL": f"{settings.STATIC_URL}sites/{site[1]}/",  # type: ignore[misc]
                    }
                )["logo_svg_square"],
                "org_id": site[0],
            }
            | (
                {
                    "promoted": True,
                    "logo_svg_only": logo_svg_urls(
                        {
                            "STATIC_SITE_URL": f"{settings.STATIC_URL}sites/{site[1]}/",  # type: ignore[misc]
                        }
                    )["logo_svg_only"],
                }
                if member_promoted_org and site[0] == member_promoted_org
                else {}
            )
            for site in sites
        ]

        summary = {
            "sites": summary_sites,
            "content": content,
            "cluster_name": cluster_name,
            "cluster_id": cluster_id,
        }

        utils.cache_set(cache_key, summary, CLUSTER_SITE_CACHE_TIMEOUT)

    return summary


ACM_EMAIL_DOMAIN = "@austcommunitymedia.com.au"


@retry(
    (requests.exceptions.HTTPError, requests.exceptions.RequestException),
    tries=3,
    delay=2,
    backoff=2,
    logger=logger,
)
def search_authors_with_retry(filters: Optional[dict[str, Any]] = None):
    """Search authors with retry logic for request errors."""
    return search_authors(filters)


@retry(
    (requests.exceptions.HTTPError, requests.exceptions.RequestException),
    tries=3,
    delay=2,
    backoff=2,
    logger=logger,
)
def clear_user_badges_with_retry(viafoura_client, uid, section_uuid):
    """Clear user badges with retry logic for request errors."""
    return viafoura_client.clear_user_badges(uid, section_uuid)


@retry(
    (requests.exceptions.HTTPError, requests.exceptions.RequestException),
    tries=3,
    delay=2,
    backoff=2,
    logger=logger,
)
def assign_user_badge_with_retry(
    viafoura_client, uid, badge_label, section_uuid
):
    """Assign user badge with retry logic for request errors."""
    return viafoura_client.assign_user_badge(uid, section_uuid, badge_label)


def get_cluster_site_settings(site: Site):
    """Get the settings of all sites in the same cluster as the given site."""
    return Settings.objects.filter(
        domain__startswith="www",
        visible=True,
        organization__in=ClusterMember.objects.filter(
            organization=site.settings.organization,
        ).values_list("cluster__members__organization"),
    )


def email_is_silverstone_author(email: str) -> bool:
    """Check if the email is a silverstone author."""
    try:
        response = search_authors_with_retry(filters={"email": email})
    except Exception:
        logger.exception(f"Error searching authors for email {email}")
        return False

    # The objects are `Author` dictshield instances. Key access on fields not
    # defined in the class raise a `KeyError` even if they are set. Getting the
    # attribute works
    return any(author.email == email for author in response["objects"])


@retry(
    (requests.exceptions.HTTPError, requests.exceptions.RequestException),
    tries=3,
    delay=2,
    backoff=2,
    logger=logger,
)
def login_viafoura_user_with_retry(
    viafoura_client, piano_client, uid, section_uuid, domain
):
    """Login viafoura user with retry logic for request errors."""
    token_response = piano_client.get_user_token(uid)
    token = token_response.get("access_token")
    return viafoura_client.login_cookie_user(section_uuid, domain, token)


def get_cluster_id_for_site(site: Site, use_cache=True) -> str:
    cluster_summary = cluster_summary_for_site(site, use_cache)

    if cluster_summary:
        return str(cluster_summary["cluster_id"])
    else:
        return DEFAULT_CLUSTER_ID_FOR_SQS
