from django.core.management.base import BaseCommand

from .process_piano_webhooks import get_sqs_client


class Command(BaseCommand):
    help = "Delete the current message from the FIFO SQS queue. Use this if the current webhook message keeps failing and unfixable to unblock the queue."

    def add_arguments(self, parser):
        parser.add_argument(
            "--queue-url",
            type=str,
            required=True,
            help="The URL of the FIFO SQS queue to delete the message from",
        )
        parser.add_argument(
            "--message-group-id",
            type=str,
            required=True,
            help="The message group ID of the message to delete",
        )

    def handle(self, *args, **options):
        sqs_client = get_sqs_client()
        queue_url = options["queue_url"]
        message_group_id = options["message_group_id"]

        # Get the current message from the queue
        response = sqs_client.receive_message(
            QueueUrl=queue_url,
            MaxNumberOfMessages=1,
            VisibilityTimeout=0,
        )

        if not response.get("Messages"):
            self.stdout.write(
                f"({message_group_id}): No messages found in the queue. Exiting."
            )
            return

        message = response["Messages"][0]

        self.stdout.write(f"({message_group_id}): Deleting message: {message}")

        sqs_client.delete_message(
            QueueUrl=queue_url,
            ReceiptHandle=message["ReceiptHandle"],
        )

        self.stdout.write(f"({message_group_id}): Message deleted.")
