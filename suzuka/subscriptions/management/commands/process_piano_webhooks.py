import json
import logging
import signal
import sys
import time
from typing import Any

import boto3
from django.conf import settings
from django.core.management.base import BaseCommand, CommandError

from suzuka.subscriptions.utils import DEFAULT_CLUSTER_ID_FOR_SQS
from suzuka.subscriptions.views import handle_webhook_sqs

logger = logging.getLogger(__name__)


def get_sqs_client():
    """
    Get the SQS client.

    Reuse an instance so it can be mocked for tests.
    """
    _sqs_client = boto3.client("sqs")

    return _sqs_client


class Command(BaseCommand):
    help = "Process Piano webhook messages from SQS queue"

    sqs_client = get_sqs_client()
    queue_url = getattr(settings, "PIANO_WEBHOOK_SQS_URL", None)

    shutdown_requested = False

    def attach_signal_handlers(self):
        signal.signal(signal.SIGINT, self.handle_signal)
        signal.signal(signal.SIGTERM, self.handle_signal)

    def add_arguments(self, parser):
        parser.add_argument(
            "--visibility-timeout",
            type=int,
            default=300,  # 5 minutes
            help="Visibility timeout in seconds for SQS messages (default: 300 seconds)",
        )
        parser.add_argument(
            "--message-group-id",
            type=str,
            default=DEFAULT_CLUSTER_ID_FOR_SQS,
            help="Message group ID to use for SQS messages (default: 'default')",
        )

    def return_messages_to_queue(self, messages: list[dict[str, Any]]):
        """
        Return messages to queue.
        """
        for message in messages:
            self.sqs_client.change_message_visibility(
                QueueUrl=self.queue_url,
                ReceiptHandle=message["ReceiptHandle"],
                VisibilityTimeout=0,
            )

    def get_messages_in_queue(
        self,
        visibility_timeout: int,
    ) -> list[dict[str, Any]]:
        """
        Get the current message from the queue.
        """
        response = self.sqs_client.receive_message(
            QueueUrl=self.queue_url,
            MaxNumberOfMessages=10,
            WaitTimeSeconds=20,
            VisibilityTimeout=visibility_timeout,
            MessageSystemAttributeNames=["MessageGroupId"],
        )

        messages = response.get("Messages", [])

        return messages

    def is_message_group_id_valid(
        self, message_group_id: str, message: dict[str, Any]
    ) -> bool:
        message_id = message["MessageId"]
        message_group_id_from_message = message["Attributes"]["MessageGroupId"]

        self.stdout.write(
            f"({message_group_id}): Message group ID from message: {message_group_id_from_message}"
        )

        if message_group_id_from_message != message_group_id:
            self.stdout.write(
                f"({message_group_id}): Message {message_id} does not belong to the current message group. Deleting message."
            )

            # We have a filter policy set in the SNS Subcription to make sure that SQS Queues only receive messages that belong to the current message group.
            # So we can safely delete the message.
            self.sqs_client.delete_message(
                QueueUrl=self.queue_url,
                ReceiptHandle=message["ReceiptHandle"],
            )
            return False

        return True

    def get_message_data(self, message: dict[str, Any]) -> dict[str, Any]:
        body_data = json.loads(message["Body"])
        sqs_data: dict[str, Any] = json.loads(body_data["Message"])
        return sqs_data

    def handle_signal(self, signum, frame):
        self.stdout.write(
            f"HANDLER: Received signal {signum} shutting down..."
        )
        self.shutdown_requested = True

    def handle(self, *args, **options):
        if not self.queue_url:
            raise CommandError("PIANO_WEBHOOK_SQS_URL is not set")

        self.attach_signal_handlers()
        visibility_timeout = options["visibility_timeout"]
        message_group_id = options["message_group_id"]
        start_time = time.time()

        processed_messages = 0

        while not self.shutdown_requested:
            try:
                self.stdout.write(
                    f"({message_group_id}): Getting messages from queue."
                )
                messages = self.get_messages_in_queue(
                    visibility_timeout,
                )

                if not messages:
                    self.stdout.write(
                        f"({message_group_id}): No messages found in queue."
                    )
                    break

                self.stdout.write(
                    f"({message_group_id}): Retrieved {len(messages)} messages"
                )

                start_time_for_messages = time.time()
                message_processed_count = 0
                for idx, message in enumerate(messages):
                    if self.shutdown_requested:
                        self.stdout.write(
                            f"({message_group_id}): HANDLER: Shutdown requested..."
                        )
                        remaining_messages = messages[idx:]

                        if remaining_messages:
                            self.stdout.write(
                                f"({message_group_id}): HANDLER: Returning {len(remaining_messages)} messages to queue"
                            )
                            self.return_messages_to_queue(remaining_messages)

                        break

                    message_id = message["MessageId"]

                    if not self.is_message_group_id_valid(
                        message_group_id, message
                    ):
                        continue

                    sqs_data = self.get_message_data(message)

                    event_type = sqs_data["event_type"]
                    event = sqs_data["event"]
                    site_id = sqs_data["site_id"]

                    self.stdout.write(
                        f"{message_id} ({message_group_id}): Event type: {event_type}, Event: {event}, Site ID: {site_id}"
                    )

                    result = handle_webhook_sqs(sqs_data)

                    self.stdout.write(
                        f"{message_id} ({message_group_id}): Result: {result}"
                    )

                    if not result["success"]:
                        if result["continue"]:
                            self.stdout.write(
                                f"{message_id} ({message_group_id}): Event {event} Event type {event_type} Webhook failed: {result}. Continuing processing."
                            )
                        else:
                            raise Exception(
                                f"{message_id} ({message_group_id}): Event {event} Event type {event_type} Webhook failed: {result}"
                            )

                    self.sqs_client.delete_message(
                        QueueUrl=self.queue_url,
                        ReceiptHandle=message["ReceiptHandle"],
                    )

                    processed_messages += 1
                    message_processed_count += 1

                elapsed_minutes = (time.time() - start_time_for_messages) / 60
                self.stdout.write(
                    f"({message_group_id}): Processed {message_processed_count}/{len(messages)} messages in {elapsed_minutes:.2f} minutes. Total processed messages: {processed_messages}"
                )
            except Exception:
                logger.exception(
                    "An unexpected error occurred while processing Piano Webhook SQS messages."
                )
                break

        elapsed_minutes = (time.time() - start_time) / 60
        self.stdout.write(
            f"({message_group_id}) Processed total of {processed_messages} messages in {elapsed_minutes:.2f} minutes"
        )

        if self.shutdown_requested:
            self.stdout.write(
                f"({message_group_id}): HANDLER: Shutting down..."
            )
            sys.exit(0)
