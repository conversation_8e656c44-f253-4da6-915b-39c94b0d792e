"""Views for subscriptions/paywall (Piano)."""

import json
import logging
from datetime import datetime
from typing import Any, Union
from urllib.parse import urljoin

import boto3
import jwt
import requests
from django.conf import settings
from django.contrib.sites.models import Site
from django.core.exceptions import ValidationError as DjangoValidationError
from django.core.mail import send_mail
from django.core.validators import EmailValidator
from django.http import Http404, HttpResponse, JsonResponse
from django.template.loader import get_template
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.views.generic import TemplateView, View
from jsonschema import (
    Draft4Validator,
    FormatChecker,
    ValidationError,
    validate,
)
from rest_framework.decorators import (
    api_view,
    authentication_classes,
    permission_classes,
)
from rest_framework.response import Response

from suzuka.api.utils import allow_piano_CORS
from suzuka.api.views.mail import should_use_remote
from suzuka.comments.viafoura import ViafouraClient
from suzuka.conf.sites import current_site
from suzuka.mailinglists.providers import PROVIDERS
from suzuka.pages.context_processor import render_context_response
from suzuka.pages.models import Page
from suzuka.pages.monaco import is_monaco_render
from suzuka.pagesapi.utils import get_decoded_data

from .models import AuthorPianoUser, EnterpriseSubscriptionSite, PianoUser
from .piano import (
    PianoClient,
    PianoException,
    RateLimitException,
    UserDoesNotExist,
)
from .utils import (
    ACM_EMAIL_DOMAIN,
    assign_user_badge_with_retry,
    clear_user_badges_with_retry,
    decrypt_piano_data,
    email_is_silverstone_author,
    get_cluster_id_for_site,
    get_cluster_site_settings,
    get_publication_aid_dict,
    login_viafoura_user_with_retry,
)

_sns_client = None


def get_sns_topic():
    """
    Get the SNS client.

    Reuse an instance so it can be mocked for tests.
    """
    global _sns_client

    if not _sns_client:
        _sns_client = boto3.client("sns")

    return _sns_client


print_bundle_schema = {
    "type": "object",
    "properties": {
        "name": {"type": "string"},
        "email": {"type": "string", "format": "email"},
        "contact_number": {"type": "string"},
        "existing": {"type": "string"},
    },
    "required": ["name", "email", "contact_number", "existing"],
}
corporate_group_subscriptions_schema = {
    "type": "object",
    "properties": {
        "name": {"type": "string", "maxLength": 128},
        "phone": {"type": "string", "maxLength": 128},
        "email": {"type": "string", "format": "email", "maxLength": 254},
        "business_name": {"type": "string", "maxLength": 128},
        "additional_comments": {"type": "string", "maxLength": 1000},
        "role": {"type": "string", "maxLength": 128},
        "seats": {"type": "string", "maxLength": 128},
        "type": {"type": "string", "maxLength": 128},
    },
    "required": [
        "name",
        "phone",
        "email",
        "business_name",
        "type",
        "role",
        "seats",
    ],
}

logger = logging.getLogger(__name__)


class SubscriptionView(TemplateView):
    page_name = ""
    page_url = ""

    def _is_allowed(self) -> None:
        site = current_site()
        return site.settings.features.pianofeature_enabled

    def _get_extra_context(self) -> dict:
        return {
            "disable_adserving": True,
            "page": Page(
                name=self.page_name,
                no_index=True,
                no_snippet=True,
                template=self.template_name,
                url=self.page_url,
            ),
            "view_type": "auth",
        }

    def get(self, request, *args, **kwargs):
        if not self._is_allowed():
            raise Http404()
        return super().get(request, *args, **kwargs)

    def render_to_response(self, context):
        if is_monaco_render():
            context.update(self._get_extra_context())
            context["full_page_render"] = True
            context["template_name"] = self.template_name
            return render_context_response(self.request, context)

        return super().render_to_response(context)


class SubscriptionMyAccountView(SubscriptionView):
    template_name = "lean_my_account.html"
    page_name = "My Account"
    page_url = "my-account"

    def _is_allowed(self) -> None:
        return current_site().settings.features.pianofeature_enabled

    def _get_extra_context(self) -> dict:
        return {
            "page": Page(
                name=self.page_name,
                no_index=True,
                no_snippet=True,
                template=self.template_name,
                url=self.page_url,
            ),
        }


class SubscriptionLoginView(SubscriptionView):
    template_name = "login.html"
    page_name = "Login"
    page_url = "login"


class SubscriptionRegisterView(SubscriptionView):
    template_name = "register.html"
    page_name = "Register"
    page_url = "register"


class SubscriptionOfferView(SubscriptionView):
    template_name = "offer.html"
    page_name = "Offer"
    page_url = "offer"


class SubscriptionPaymentView(SubscriptionView):
    template_name = "payment.html"
    page_name = "Payment"
    page_url = "payment"


class SubscriptionCompleteProfileView(SubscriptionView):
    template_name = "complete_profile.html"
    page_name = "Complete Profile"
    page_url = "complete-profile"


class SubscriptionPromoSurveyView(SubscriptionView):
    template_name = "promo_survey.html"
    page_name = "Promo Survey"
    page_url = "promo-survey"


class SubscriptionResetPasswordView(SubscriptionView):
    template_name = "reset_password.html"
    page_name = "Reset Password"
    page_url = "reset-password"


class SubscriptionMagicLinkView(SubscriptionView):
    template_name = "magic_link.html"
    page_name = "Magic Link"
    page_url = "magic-link"


def send_print_bundle_email(req):
    req["publication"] = current_site().name
    context = {"ctx": req}
    # Only using a text template, so turn off autoescaping.
    context.update(autoescape=False)
    mail_template = get_template("emails/print_bundle.txt").render(context)
    mail_from = getattr(settings, "PRINT_BUNDLE_EMAIL_FROM", "")
    mail_to = getattr(settings, "PRINT_BUNDLE_EMAIL_TO", [])
    mail_subject = "New print and digital bundle request"
    if mail_to:
        success = send_mail(
            mail_subject,
            mail_template,
            mail_from,
            mail_to,
            fail_silently=True,
        )
        return JsonResponse({"success": success, "HTTP_CODE": 200})
    else:
        return JsonResponse({"success": 0, "HTTP_CODE": 200})


def send_corporate_group_subscriptions_email(req):
    req["publication"] = current_site().name
    context = {"ctx": req}
    # Only using a text template, so turn off autoescaping.
    context.update(autoescape=False)
    mail_template = get_template(
        "emails/corporate_group_subscriptions.txt",
    ).render(
        context,
    )
    mail_from = getattr(settings, "GROUP_SUBSCRIPTIONS_EMAIL_FROM", "")
    mail_to = getattr(settings, "GROUP_SUBSCRIPTIONS_EMAIL_TO", [])
    mail_subject = "New corporate group subscriptions bundle request"
    if mail_to:
        success = send_mail(
            mail_subject,
            mail_template,
            mail_from,
            mail_to,
            fail_silently=True,
        )
        return JsonResponse({"success": success, "HTTP_CODE": 200})
    else:
        return JsonResponse({"success": 0, "HTTP_CODE": 200})


@api_view(["POST"])
@authentication_classes([])
@permission_classes([])
def print_bundle_email(request):
    req = request.data.copy()
    try:
        validate(req, print_bundle_schema, format_checker=FormatChecker())
    except ValidationError:
        v = Draft4Validator(
            print_bundle_schema,
            format_checker=FormatChecker(),
        )
        x = [e.message for e in v.iter_errors(req)]
        return Response({"Exception": x[0], "HTTP_CODE": 400})
    return send_print_bundle_email(req)


@api_view(["POST"])
@authentication_classes([])
@permission_classes([])
def corporate_group_subscriptions_email(request):
    req = request.data.copy()
    try:
        validate(
            req,
            corporate_group_subscriptions_schema,
            format_checker=FormatChecker(),
        )
    except ValidationError:
        v = Draft4Validator(
            corporate_group_subscriptions_schema,
            format_checker=FormatChecker(),
        )
        x = [e.message for e in v.iter_errors(req)]
        return Response({"Exception": x[0], "HTTP_CODE": 400})
    return send_corporate_group_subscriptions_email(req)


WEBHOOK_CALLBACKS = {}


def handle_webhook(*webhooks):
    """
    Decorator to register a function to Piano webhooks.

    Only one function can be assigned to a webhook at a time.

    Functions that raise any exception causes Piano to retry the webhook.
    """

    def decorator(function):
        """Register the function."""
        for webhook in webhooks:
            WEBHOOK_CALLBACKS.setdefault(webhook, []).append(function)

        return function

    return decorator


class PianoWebhookView(View):
    """
    Handle Piano webhooks.

    See https://docs.piano.io/webhooks/
    """

    def get(self, *args, **kwargs) -> HttpResponse:
        """Route a Piano webhook to a SNS topic."""

        data = self.request.GET.get("data")
        response = HttpResponse()

        if not data:
            return response

        site = current_site()
        site_settings = site.settings

        if (
            not site_settings.features.pianofeature_enabled
            or not site_settings.pianofeature_piano_aid
            or not site_settings.pianofeature_piano_private_key
            or not site_settings.pianofeature_piano_api_token
        ):
            return response

        data = decrypt_piano_data(
            site_settings.pianofeature_piano_private_key,
            data,
        )

        json_data: dict[str, Any] = json.loads(data)

        if not (event_type := json_data.get("type")):
            return response

        event = json_data.get("event")

        webhook_key = f"{event_type}.{event}"

        if webhook_key not in WEBHOOK_CALLBACKS:
            return response

        uid = json_data.get("uid")
        if not uid:
            return response

        webhook_data = {
            "site_setting_id": site_settings.id,
            "site_id": site.id,
            "site_domain": site.domain,
            "user_id": uid,
            "event_type": event_type,
            "event": event,
            "json_data": json_data,
            "received_at": datetime.now().isoformat(),
        }

        try:
            # Disabling cache to always get the updated cluster for the site
            site_cluster_id = get_cluster_id_for_site(site, use_cache=False)
            sns_client = get_sns_topic()
            logger.info(f"Sending message to SNS: {site_cluster_id}")
            sns_client.publish(
                TopicArn=settings.PIANO_WEBHOOK_SNS_TOPIC,
                Message=json.dumps(webhook_data),
                MessageGroupId=site_cluster_id,
                MessageAttributes={
                    "site_cluster_id": {
                        "DataType": "String",
                        "StringValue": site_cluster_id,
                    },
                },
            )
        except Exception as e:
            logger.exception(f"Error sending message to SNS: {e}")
            return HttpResponse(
                status=500, content=f"Error sending message to SNS: {e}"
            )

        return response


def handle_webhook_response(
    success: bool, message: str, continue_processing: bool = False
) -> dict[str, Any]:
    return {
        "success": success,
        "message": message,
        "continue": continue_processing,
    }


def handle_webhook_sqs(sqs_data: dict[str, Any]) -> dict[str, Any]:
    user_id = sqs_data["user_id"]
    event_type = sqs_data["event_type"]
    event = sqs_data["event"]
    json_data = sqs_data["json_data"]
    site_id = sqs_data["site_id"]

    try:
        site = Site.objects.select_related(
            "settings", "settings__features"
        ).get(id=site_id)
        site_settings = site.settings
    except Site.DoesNotExist:
        logger.warning(f"Site {site_id} does not exist")
        return handle_webhook_response(False, f"Site {site_id} does not exist")

    try:
        piano_client = PianoClient(
            site_settings.pianofeature_piano_aid,
            site_settings.pianofeature_piano_api_token,
        )

        user = piano_client.get_user(user_id)

        for method in WEBHOOK_CALLBACKS[f"{event_type}.{event}"]:
            method(piano_client, site_settings, user, site, **json_data)
    except RateLimitException:
        logger.warning(
            "Piano Rate Limit has been exceeded while processing Piano Webhook SQS message."
        )
        return handle_webhook_response(False, "Rate limit exceeded")
    except UserDoesNotExist:
        logger.warning(
            f"User {user_id} does not exist while processing Piano Webhook SQS message."
        )
        # Continue processing even if the user does not exist
        # to avoid infinite retries. The piano user is required to properly process
        # the webhook.
        return handle_webhook_response(
            False, f"User {user_id} does not exist", True
        )
    except Exception as e:
        logger.exception(
            "An unexpected error occurred while handling a Piano Webhook SQS message."
        )
        return handle_webhook_response(False, f"Error handling webhook: {e}")

    return handle_webhook_response(
        True, f"Webhook {event_type}.{event} handled successfully"
    )


@handle_webhook(
    "user_created.user_created",
)
def check_enterprise_subscription(
    piano_client, site_settings, user, site, **data
):
    phoenix_enterprise_register_url = urljoin(
        settings.PHOENIX_API_HOST, "enterprise/register/"
    )
    headers = {
        "Origin": f"https://{site_settings.domain}",
    }
    try:
        req_response = requests.post(
            phoenix_enterprise_register_url,
            headers=headers,
            json={
                "uid": user["uid"],
                "aid": site_settings.pianofeature_piano_aid,
            },
            timeout=30,
        )
        req_response.raise_for_status()
    except (
        requests.exceptions.HTTPError,
        requests.exceptions.RequestException,
    ) as err:
        if settings.DEBUG:
            logger.error(err)
        else:
            logger.warning(f"Phoenix enterprise registration request: {err}")
        return


email_validator = EmailValidator()


@handle_webhook(
    # Hook every event where user details or subscriptions may have changed
    "access_granted.new_purchase",
    "access_granted.payment_verified",
    "access_granted.free_promo_redemption",
    "access_granted.new_registration_conversion",
    "access_granted.free_access_granted",
    "access_modified.subscription_updated",
    "access_modified.subscription_auto_renewed",
    "access_modified.subscription_manually_renewed",
    "access_modified.access_modified",
    "access_modified.grace_period_extension_on_renewal",
    "access_revoked.subscription_canceled",
    "access_revoked.subscription_expired",
    "access_revoked.access_ended",
    "term_change.term_change",
    "user_created.user_created",
    "user_updated.user_updated",
    "user_disabled.user_disabled",
    "user_address_updated.user_address_updated",
)
def update_user_mail_details(piano_client, site_settings, user, site, **data):
    """
    Update a Piano user's details in the mail provider.

    This could be optimized further. Currently updates every field on every
    event.
    """
    if not site_settings.features.mailfeature_enabled:
        return

    subscription = piano_client.get_user_subscription(user)

    email = user["email"]

    # Piano users may have invalid emails. Return without errors to prevent
    # exceptions and infinite retrying of webhooks
    try:
        email_validator(email)
    except DjangoValidationError:
        return

    provider = PROVIDERS[site_settings.mailfeature_provider]
    list_id = site_settings.mailfeature_list_id

    # If the user has changed their email, update the existing record in the
    # mailing list, if it exists. Otherwise, it creates a new record and both
    # the new and old email appear in the list.
    # Piano does not provide a way of knowing that the email has changed, so
    # must keep a separate record in Suzuka.
    try:
        piano_user = PianoUser.objects.get(uid=user["uid"])
    except PianoUser.DoesNotExist:
        PianoUser.objects.create(uid=user["uid"], email=email)
        # new users will be automatically subscribed to default newsletters of the site where they first signed up.
        provider.set_subscriber_active(email)
        groups_list = provider.get_groups(list_id)
        if groups_list:
            default_interests = site_settings.mailfeature_default_interests
            interests_to_set = {
                interest: True
                for interest in default_interests
                if interest in groups_list
            }
            if interests_to_set:
                provider.set_groups(
                    list_id,
                    email,
                    interests_to_set,
                    site,
                    should_use_remote(site_settings),
                )
    else:
        if piano_user.email != email:
            # Update the old record (change email)
            did_update = provider.update_subscriber_email(
                list_id,
                piano_user.email,
                email,
                site,
            )

            # Update the `PianoUser` record
            piano_user.email = email
            piano_user.save()

            # If the user existed and was updated, return. Otherwise, flow
            # through to the creation process
            if did_update:
                return

    provider.create_or_update_subscriber(
        list_id,
        email,
        provider.piano_user_to_fields(user, subscription),
        site,
        should_use_remote(site_settings),
    )


@handle_webhook(
    "access_granted.new_purchase",
    "access_granted.payment_verified",
    "access_granted.free_promo_redemption",
)
def access_granted_breaking_news(
    piano_client, site_settings, user, site, **data
):
    """Subscribe new subscribers to Breaking News newsletter"""
    if not site_settings.features.mailfeature_enabled:
        return

    email = user["email"]

    # Piano users may have invalid emails. Return without errors to prevent
    # exceptions and infinite retrying of webhooks
    try:
        email_validator(email)
    except DjangoValidationError:
        return

    provider = PROVIDERS[site_settings.mailfeature_provider]
    list_id = site_settings.mailfeature_list_id
    provider.set_groups(
        list_id,
        user["email"],
        {"Breaking": True},
        site,
        should_use_remote(site_settings),
    )


@handle_webhook("access_revoked.subscription_canceled")
def access_revoked_retently(piano_client, site_settings, user, site, **data):
    """Send users to Retently when their access is revoked."""
    if (
        not site_settings.features.retentlyfeature_enabled
        or not site_settings.retentlyfeature_retently_track_revoked_access
    ):
        return

    email = user.get("email")
    first_name = user.get("first_name")
    last_name = user.get("last_name")

    if not email:
        return

    subscriber = {
        "email": email,
        "company": site_settings.name,
        "tags": ("api", "exit", site_settings.publication),
    }

    if first_name:
        subscriber["first_name"] = first_name

    if last_name:
        subscriber["last_name"] = last_name

    requests.post(
        url="https://app.retently.com/api/v2/nps/customers",
        headers={
            "Authorization": f"api_key={settings.RETENTLY_API_KEY}",
        },
        json={
            "subscribers": (subscriber,),
        },
    )


class PianoTokenView(View):
    """
    Validate a Piano user token for Viafoura comments.

    This view is called from Viafoura's servers when authenticating a user for
    interacting with a story's comments section.
    """

    def get(self, *args, **kwargs) -> Union[HttpResponse, JsonResponse]:
        """Validate the token and provide user details."""
        try:
            token = self.request.GET["__utp"]
        except KeyError:
            return HttpResponse(status=403)

        try:
            data = get_decoded_data(token)
        except jwt.InvalidTokenError:
            return HttpResponse(status=403)

        email = data["email"]

        display_name = " ".join(
            (
                data.get("given_name", ""),
                data.get("family_name", ""),
            )
        ).strip()

        # If the user has not set any names, make something from their email
        if not display_name:
            display_name = email.split("@")[0]

        return JsonResponse(
            {
                "displayName": display_name,
                "email": email,
                "uid": data["sub"],
            }
        )


def error(message: str) -> JsonResponse:
    return JsonResponse({"success": False, "message": message})


class EnterpriseSubscriptionsView(View):
    """API endpoint for the fetching enterprise subscriptions on the current site."""

    def dispatch(self, request, *args, **kwargs):
        """Enable CORS."""
        response = super().dispatch(request, *args, **kwargs)

        return allow_piano_CORS(request, response)

    def get(self, request, *args, **kwargs):
        """Return the response as JSON."""
        try:
            return JsonResponse(
                {
                    "success": True,
                    "enterprises": [
                        {
                            "icon_url": f"{settings.STATIC_URL}{es.enterprise_subscription.icon_url}",
                            "name": es.enterprise_subscription.name,
                            "piano_connection_name": es.enterprise_subscription.piano_connection_name,
                        }
                        for es in EnterpriseSubscriptionSite.objects.get_active_enterprise_subscriptions_for_site()
                    ],
                }
            )
        except requests.exceptions.RequestException:
            return JsonResponse(
                {
                    "success": False,
                },
                status=500,
            )


class OriginalPublicationView(View):
    """API endpoint for the original publication."""

    @method_decorator(csrf_exempt)
    def dispatch(self, request, *args, **kwargs):
        """Enable CORS."""
        response = super().dispatch(request, *args, **kwargs)

        return allow_piano_CORS(request, response)

    def get(self, request, *args, **kwargs):
        site_settings = current_site().settings
        site_aid = site_settings.pianofeature_piano_aid

        if not site_settings.features.pianofeature_enabled:
            return error("Site does not support piano integration")

        uid = request.GET.get("uid")
        if not uid:
            return error("No uid provided")

        piano_client = PianoClient(
            site_aid,
            site_settings.pianofeature_piano_api_token,
        )

        try:
            response = piano_client.get_user_accesses_from_request(uid)
        except PianoException:
            return error(f"Invalid uid - {uid}")

        if response is None:
            return error("No access found")

        try:
            original_aid = response["accesses"][0]["resource"]["aid"]
        except (KeyError, IndexError):
            return error("No access found")

        if original_aid == site_aid:
            return JsonResponse(
                {"success": True, "isOriginalPublication": True}, safe=False
            )
        else:
            publication_aid_dict = get_publication_aid_dict()
            if publication_aid_dict is None:
                return error("Piano aid and publication dictionary is empty")

            try:
                publication = publication_aid_dict[original_aid]
            except KeyError:
                return error(
                    f"No publication found mapping for original_aid: {original_aid}"
                )

            return JsonResponse(
                {
                    "success": True,
                    "isOriginalPublication": False,
                    "originalPublication": publication,
                },
                safe=False,
            )


@csrf_exempt
def set_original_publication(request):
    if request.method != "POST":
        return error("[Set Original Publication]: Invalid request")

    site_settings = current_site().settings
    site_aid = site_settings.pianofeature_piano_aid

    if not site_settings.features.pianofeature_enabled:
        return error(
            "[Set Original Publication]: Site does not support piano integration"
        )

    uid = request.POST.get("uid", None)
    if not uid:
        return error("[Set Original Publication]: No uid provided")

    piano_client = PianoClient(
        site_aid,
        site_settings.pianofeature_piano_api_token,
    )

    try:
        user_profile = piano_client.get_user(uid)
    except RateLimitException:
        return error("[Set Original Publication]: Rate limit")
    except UserDoesNotExist:
        return error("[Set Original Publication]: Issue while searching user")

    for field in user_profile["custom_fields"]:
        if field["fieldName"] == "original_publication":
            if field["value"]:
                return JsonResponse({"success": True})

    custom_fields = {"original_publication": True}
    try:
        piano_client.update_user(uid, custom_fields=custom_fields)
    except PianoException:
        logger.warning(
            f"[Set Original Publication]: User update failed with uid - {uid}"
        )
        return error("[Set Original Publication]: User update failed")

    return JsonResponse({"success": True})


@handle_webhook(
    "access_granted.new_purchase",
    "access_granted.payment_verified",
    "access_granted.free_promo_redemption",
    "access_granted.new_registration_conversion",
    "access_granted.free_access_granted",
)
def access_granted_viafoura(piano_client, site_settings, user, site, **data):
    uid = user["uid"]
    email = user["email"]
    if ACM_EMAIL_DOMAIN in email or email_is_silverstone_author(email):
        logger.info(
            f"Skip setting viafoura subscriber badge for acm author: {email}"
        )
        return

    all_cluster_site_settings = get_cluster_site_settings(site)
    if all_cluster_site_settings:
        for cluster_site_settings in all_cluster_site_settings:
            if not cluster_site_settings.viafoura_section_uuid:
                logger.warning(
                    f"No viafoura section uuid found for {cluster_site_settings.domain}"
                )
                continue
            viafoura_client = ViafouraClient()
            piano_client.set_auth(
                cluster_site_settings.pianofeature_piano_aid,
                cluster_site_settings.pianofeature_piano_api_token,
            )
            try:
                # Log the user in to Viafoura to ensure they have an account
                login_viafoura_user_with_retry(
                    viafoura_client,
                    piano_client,
                    uid,
                    cluster_site_settings.viafoura_section_uuid,
                    cluster_site_settings.domain,
                )
                logger.info(
                    f"Logged in viafoura user for {uid} on {cluster_site_settings.domain}"
                )
            except Exception:
                logger.exception(
                    f"Error logging in viafoura user for access granted {uid} on {cluster_site_settings.domain}"
                )

            try:
                assign_user_badge_with_retry(
                    viafoura_client,
                    uid,
                    "piano_subscriber",
                    cluster_site_settings.viafoura_section_uuid,
                )
                logger.info(
                    f"Assigned piano_subscriber badge to {cluster_site_settings.domain} for {uid}"
                )
            except Exception:
                logger.exception(
                    f"Error assigning badge to user for access granted {uid}"
                )


@handle_webhook(
    "access_revoked.subscription_canceled",
    "access_revoked.subscription_expired",
    "access_revoked.access_revoked",
    "access_revoked.access_ended",
    "user_disabled.user_disabled",
)
def access_revoked_viafoura(piano_client, site_settings, user, site, **data):
    uid = user["uid"]
    cluster_site_settings = get_cluster_site_settings(site)

    if cluster_site_settings:
        for site_setting in cluster_site_settings:
            if not site_setting.viafoura_section_uuid:
                logger.warning(
                    f"No viafoura section uuid found for {site_setting.domain}"
                )
                continue
            viafoura_client = ViafouraClient()
            logger.info(f"Clearing badges on {site_setting.domain} for {uid}")
            try:
                clear_user_badges_with_retry(
                    viafoura_client,
                    uid,
                    site_setting.viafoura_section_uuid,
                )
            except Exception:
                logger.exception(
                    f"Error clearing badges from user for access revoked {uid}"
                )


@handle_webhook(
    "user_created.user_created",
    "user_updated.user_updated",
)
def user_created_acm_author(piano_client, site_settings, user, site, **data):
    email = user["email"]

    if ACM_EMAIL_DOMAIN in email or email_is_silverstone_author(email):
        AuthorPianoUser.objects.update_or_create(
            email=email,
            defaults={
                "uid": user.get("uid"),
                "aid": site_settings.pianofeature_piano_aid,
            },
        )
