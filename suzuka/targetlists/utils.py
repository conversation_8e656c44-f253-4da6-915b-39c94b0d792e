import logging
import re
from typing import Optional

from requests import ConnectionError
from slumber_party.utils import persistent_api_session

from suzuka.conf.sites import current_site_id
from suzuka.stories.utils import cache_get, cache_set
from suzuka.targetlists.settings import TARGET_LIST_CACHE_TIMEOUT

LIST_REGEX = re.compile("/api/list/(\d+)/")
SITE_ID_REGEX = re.compile("/api/site/(\d+)/")


def list_api():
    return persistent_api_session("dijon")


logger = logging.getLogger(__name__)


def get_target_list_ids(site_id: Optional[int] = None) -> str:
    target_list_ids = cache_get(cache_key(site_id=site_id))

    if target_list_ids is None:
        target_list_ids = cache_target_lists(site_id=site_id)
    return target_list_ids


def cache_key(site_id=None):
    if site_id is None:
        site_id = current_site_id()
    return "target-lists-%s" % site_id


def cache_target_lists(site_id: Optional[int] = None) -> str:
    lookup = {
        "members__resource": site_resource(site_id=site_id),
        "limit": 0,
    }
    try:
        target_lists = list_api().list.get(**lookup)["objects"]
    except ConnectionError as e:
        logger.error(e)
        return ""
    target_list_ids = ",".join(
        [str(target_list.id) for target_list in target_lists]
    )
    cache_set(
        cache_key(site_id=site_id), target_list_ids, TARGET_LIST_CACHE_TIMEOUT
    )
    return target_list_ids


def site_resource(site_id=None):
    if site_id is None:
        site_id = current_site_id()
    return "/api/site/%s/" % site_id


def get_target_members_ids(target_id):
    target_member_ids = cache_get(cache_member_key(target_id))
    if target_member_ids is None:
        target_member_ids = cache_target_members(target_id)
    return target_member_ids


def cache_member_key(target_id):
    return "target-members-%s" % target_id


def cache_target_members(target_id):
    lookup = {
        "limit": 0,
    }
    target_members = []
    try:
        target_members = [
            member._data
            for member in list_api()
            .list(target_id)
            .member.get(**lookup)["objects"]
        ]
    except (ConnectionError, KeyError) as e:
        logger.error(f"target_members: {target_id} {e}")
        return []
    try:
        target_member_ids = [
            int(SITE_ID_REGEX.findall(member["resource"])[0])
            for member in target_members
        ]
    except (KeyError, IndexError) as e:
        logger.error(f"target_members: {target_id} {e}")
        return []
    cache_set(
        cache_member_key(target_id),
        target_member_ids,
        TARGET_LIST_CACHE_TIMEOUT,
    )
    return target_member_ids


def get_site_list_ids(target_lists):
    """
    Take a `Site.target_list` attribute and return a list of target ids.

    :param target_lists: a list of endpoints, eg, ['/api/list/20/']
    :return: a list of integers corresponding to site ids.
    """
    site_list_ids = []
    for end_point in target_lists:
        try:
            target_id = int(LIST_REGEX.findall(end_point)[0])
        except IndexError as e:
            logger.warning(f"target_members: {end_point} {e}")
            continue
        site_list_ids += get_target_members_ids(target_id)
    return site_list_ids
